// https://docs.expo.dev/guides/using-eslint/
module.exports = {
  ignorePatterns: ['wasm/**'],
  extends: ['expo', 'airbnb', 'prettier'],
  plugins: ['prettier', 'perfectionist', 'react', 'react-hooks'],
  settings: {
    'import/resolver': {
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx'], // Add .jsx and .tsx extensions here
      },
    },
  },

  rules: {
    'no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    'prettier/prettier': 'error',
    'react-hooks/rules-of-hooks': 'error', // Ensures hooks are used correctly
    'react-hooks/exhaustive-deps': 'error', // Checks dependencies for hooks like useEffect, useMemo, useCallback
    'react/jsx-filename-extension': 'off',
    'react/forbid-prop-types': 'off',
    'no-shadow': 'off',
    'react/function-component-definition': [
      1,
      {
        namedComponents: 'arrow-function',
      },
    ],
    'import/extensions': [
      'error',
      'ignorePackages',
      {
        js: 'never',
        jsx: 'never',
        ts: 'never',
        tsx: 'never',
      },
    ],
    'no-underscore-dangle': 'off', // Allow dangling underscores in identifiers
    'eslint-disable import/no-unused-modules': 'off',
    'consistent-return': 'off',
    'import/no-unused-modules': 'error',
    'import/no-unresolved': 'error',
    'react/react-in-jsx-scope': 'off',
    'react/jsx-props-no-spreading': 'off',
    'import/no-extraneous-dependencies': 'off',
  },
  overrides: [
    {
      // Disable import/no-unused-modules for platform-specific files
      files: ['**/*.js', '**/*.jsx', '**/*.ts', '**/*.tsx'],
      rules: {
        'import/no-unused-modules': 'off',
      },
    },
  ],
  globals: {
    setTimeout: false,
    clearTimeout: false,
    setInterval: false,
    clearInterval: false,
    EMPTY_OBJECT: false,
    EMPTY_ARRAY: false,
    NULL_FUN: false,
    localStorage: false,
    getCurrentTime: false,
    getApolloClient: false,
  },
  parserOptions: {
    ecmaVersion: 2020,
    ecmaFeatures: {
      jsx: true,
    },
  },
};
