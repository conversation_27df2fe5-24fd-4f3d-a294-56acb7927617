import { Redirect, Stack, usePathname } from 'expo-router';
import React, { useEffect } from 'react';
import _includes from 'lodash/includes';
import { StyleSheet, View } from 'react-native';

import DarkTheme from '@/src/core/constants/themes/dark';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { INITIAL_ROUTE_KEY } from '@/src/core/constants/appConstants';

import { setStorageItemAsync } from 'core/hooks/useStorageState';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import APP_LAYOUT_CONSTANTS from 'core/constants/appLayout';
import WithRightPaneWidget from 'shared/WithRightPaneWidget';
import { WithUserActivityContextProvider } from 'core/contexts/UserActivityContext';
import useFeedStore from '@/src/store/useFeedStore';
import { UserSettingsProvider } from 'core/contexts/UserSettingsContext';
import ShareResultModalContainer from 'shared/ShareResultModal';
import AnnouncementHandler from 'shared/Announcement/AnnouncementHandler';
import OfflineHeader from '@/src/components/shared/OfflineHeader';
import InAppToast from 'molecules/InAppToast';
import useListeners from '@/src/core/hooks/useListeners';
import Loading from 'atoms/Loading';
import Navbar from '../../src/components/shared/Navbar';
import UserEventOverlays from '../../src/overlays/pages/UserEventOverlays';

const screenOptions = {
  headerShown: false,
  headerTitle: () => '',
  tabBarActiveTintColor: DarkTheme.colors.secondary,
  headerStyle: {
    padding: 16,
  },
  tabBarStyle: {
    backgroundColor: DarkTheme.colors.primary,
  },
  headerTintColor: 'white',
  headerTitleStyle: {
    fontFamily: 'Montserrat-700',
  },
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    height: '100%',
    width: '100%',
    alignSelf: 'center',
    maxWidth: APP_LAYOUT_CONSTANTS.APP_MAX_WIDTH,
  },
});

const Home = React.memo(() => {
  const { isMobile } = useMediaQuery();

  return (
    <WithUserActivityContextProvider>
      <UserSettingsProvider>
        <View style={styles.container}>
          {!isMobile && <Navbar />}
          <OfflineHeader />
          <AnnouncementHandler />
          <WithRightPaneWidget>
            <Stack screenOptions={screenOptions}>
              <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
              <Stack.Screen name="play-time" options={{ headerShown: false }} />
              <Stack.Screen name="practice" options={{ headerShown: false }} />
              <Stack.Screen name="search" options={{ headerShown: false }} />
            </Stack>
          </WithRightPaneWidget>
          <UserEventOverlays />
          <ShareResultModalContainer key="ShareResultModalContainer" />
          <InAppToast key="InAppToast" />
        </View>
      </UserSettingsProvider>
    </WithUserActivityContextProvider>
  );
});

const HomeLayout = React.memo(() => {
  useListeners();
  return <Home />;
});

const HomeLayoutContainer = () => {
  const pathname = usePathname();

  const { session, isReady } = useSession();
  const { fetchFeeds } = useFeedStore((state) => ({
    fetchFeeds: state.fetchFeeds,
  }));

  useEffect(() => {
    fetchFeeds();
  }, [fetchFeeds]);

  if (!session) {
    if (_includes(pathname?.split?.('/'), 'play')) {
      setStorageItemAsync(INITIAL_ROUTE_KEY, pathname);
    }
    return <Redirect href="/" />;
  }

  if (!isReady) {
    return <Loading label="Loading" />;
  }

  return <HomeLayout />;
};

export default React.memo(HomeLayoutContainer);
