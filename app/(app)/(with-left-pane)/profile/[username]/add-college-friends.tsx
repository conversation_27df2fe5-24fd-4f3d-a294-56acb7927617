import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import useIsAddCollegeFeatureAvailable from '@/src/hooks/features/useIsAddCollegeFeatureAvailable';
import AddCollegeFriendsScreen from '@/src/modules/friends/pages/AddCollegeFriendsScreen';
import { useEffect } from 'react';
import Analytics from 'core/analytics';
import { Redirect } from 'expo-router';

const AddCollegeFriendsPageContainer = () => {
  const isAddCollegeFeatureAvailable = useIsAddCollegeFeatureAvailable();

  useEffect(() => {
    Analytics.track(ANALYTICS_EVENTS.PROFILE.VIEWED_ADD_COLLEGE_FRIENDS_PAGE);
  }, []);

  if (!isAddCollegeFeatureAvailable) {
    return <Redirect href="/home" />;
  }

  return <AddCollegeFriendsScreen />;
};

export default AddCollegeFriendsPageContainer;
