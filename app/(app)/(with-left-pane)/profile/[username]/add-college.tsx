import AddCollegePage from 'modules/profile/pages/AddCollegePage';
import useIsAddCollegeFeatureAvailable from '@/src/hooks/features/useIsAddCollegeFeatureAvailable';
import { useEffect } from 'react';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import Analytics from '@/src/core/analytics';
import { Redirect } from 'expo-router';

const AddCollegePageContainer = () => {
  const isAddCollegeFeatureAvailable = useIsAddCollegeFeatureAvailable();

  useEffect(() => {
    Analytics.track(ANALYTICS_EVENTS.PROFILE.VIEWED_ADD_COLLEGE_PAGE);
  }, []);

  if (!isAddCollegeFeatureAvailable) {
    return <Redirect href="/home" />;
  }

  return <AddCollegePage />;
};

export default AddCollegePageContainer;
