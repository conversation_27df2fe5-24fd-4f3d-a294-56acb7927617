import React, { useCallback, useState } from 'react';
import { Dropdown } from 'react-native-element-dropdown';
import { withOpacity } from 'core/utils/colorUtils';
import styles from './Dropdown.style';
import dark from '../../../core/constants/themes/dark';
import { DropdownProps } from './types';

const DropdownComponent = (props: DropdownProps) => {
  const {
    testId,
    data,
    onChange,
    value: initialValue,
    placeholderText,
    searchPlaceholderText,
    search = true,
    style
  } = props;

  const [isFocus, setIsFocus] = useState(false);

  const onChangeDropDownItems = useCallback(
    (item) => {
      onChange?.(item.value);
      setIsFocus(false);
    },
    [onChange],
  );

  return (
    <Dropdown
      testID={testId}
      style={[
        styles.dropdown,
        style,
        isFocus && { borderColor: dark.colors.tertiary },
      ]}
      placeholderStyle={styles.placeholderStyle}
      selectedTextStyle={styles.selectedTextStyle}
      inputSearchStyle={styles.inputSearchStyle}
      containerStyle={{
        backgroundColor: dark.colors.primary,
        borderRadius: 16,
        borderColor: dark.colors.tertiary,
      }}
      iconStyle={styles.iconStyle}
      data={data}
      search={search}
      maxHeight={300}
      labelField="label"
      valueField="value"
      itemTextStyle={styles.label}
      placeholder={!isFocus ? placeholderText : '...'}
      searchPlaceholder={searchPlaceholderText}
      value={initialValue}
      onFocus={() => setIsFocus(true)}
      onBlur={() => setIsFocus(false)}
      onChange={onChangeDropDownItems}
      renderLeftIcon={() => null}
      activeColor={withOpacity(dark.colors.background, 0.7)}
    />
  );
};

export default DropdownComponent;
