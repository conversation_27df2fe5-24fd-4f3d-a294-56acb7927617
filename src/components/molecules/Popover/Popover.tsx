import React from 'react';
import { Overlay } from '@rneui/base';
import _isNil from 'lodash/isNil';
import _isFunction from 'lodash/isFunction';
import styles from './Popover.style';
import { PopoverProps, PopoverState } from './types';

const DEFAULT_ANIMATION_DURATION = 300;

let instance: Popover | null = null;

class Popover extends React.PureComponent<PopoverProps, PopoverState> {
  constructor(props: PopoverProps) {
    super(props);
    this.state = {
      isVisible: false,
      content: null,
      style: undefined,
      overlayLook: false,
      backdropStyle: null,
      animationType: 'fade',
      onBackdropPress: undefined,
    };
    instance = this;
  }

  static getInstance(): Popover | null {
    return instance;
  }

  componentWillUnmount() {
    instance = null;
  }

  render() {
    const {
      isVisible,
      content,
      style,
      overlayLook = false,
      animationType,
      backdropStyle,
    } = this.state;

    if (!isVisible && _isNil(content)) {
      return null;
    }

    return (
      <Overlay
        isVisible={isVisible}
        overlayStyle={[styles.container, style]}
        backdropStyle={[
          !overlayLook
            ? { backgroundColor: 'transparent', padding: 0 }
            : { padding: 0 },
          backdropStyle,
        ]}
        // backdropTransitionOutTiming={0}
        // hideModalContentWhileAnimating={true}
        // useNativeDriver={true}
        onBackdropPress={this.handleOnBackdropPress}
        statusBarTranslucent
        // propagateSwipe={true}
        animationType={animationType}
      >
        {content}
      </Overlay>
    );
  }

  showPane = (
    props: Pick<PopoverState, 'content'> &
      Partial<Omit<PopoverState, 'isVisible' | 'content'>>,
  ) => {
    this.setState({
      ...props,
      isVisible: true,
    });
  };

  handleClose = () => {
    this.setState(
      {
        isVisible: false,
      },
      () => {
        setTimeout(() => {
          this.setState({
            content: null,
          });
        }, DEFAULT_ANIMATION_DURATION);
      },
    );
  };

  handleOnBackdropPress = () => {
    const { onBackdropPress } = this.state;
    if (!_isNil(onBackdropPress) && _isFunction(onBackdropPress)) {
      onBackdropPress?.();
      this.handleClose();
      return;
    }
    this.handleClose();
  };

  closePane = () => {
    this.handleClose();
  };
}

const defaultPopoverShowProps: Partial<
  Omit<PopoverState, 'isVisible' | 'content'>
> = {
  style: undefined,
  overlayLook: false,
  backdropStyle: null,
  animationType: 'fade',
  onBackdropPress: undefined,
};

export function showPopover(
  props: Pick<PopoverState, 'content'> &
    Partial<Omit<PopoverState, 'isVisible' | 'content'>>,
) {
  const paneInstance = Popover.getInstance();
  if (paneInstance) {
    const showProps = {
      ...defaultPopoverShowProps,
      ...props,
    };
    paneInstance.showPane(showProps);
  }
}

export function closePopover() {
  const paneInstance = Popover.getInstance();
  if (paneInstance) {
    paneInstance.closePane();
  }
}

export const PopoverComponent = React.forwardRef<Popover, any>((props, ref) => (
  <Popover {...props} ref={ref} />
));

export default PopoverComponent;

export { PopoverComponent as Component };
