import { createContext, useContext } from 'react';
import { HistoryEntry, PathConnection, PuzzleGrid } from './types';

export interface MathMazePuzzleState {
  gridSize: number;
  puzzle: PuzzleGrid | null;
  path: string[];
  expression: string;
  tileSize: number;
  target: number | null;
  seconds: number;
  startTime: number;
  isDragging: boolean;
  pathConnections: PathConnection[];
  history: HistoryEntry[];
  historyIndex: number;
}

export interface MathMazePuzzleContextValue {
  state: MathMazePuzzleState;
  onAction: (action: { type: string; payload?: any }) => void;
}

export const MathMazePuzzleContext = createContext<
  MathMazePuzzleContextValue | undefined
>(undefined);

export const useMathMazePuzzle = () => {
  const context = useContext(MathMazePuzzleContext);
  if (!context) {
    throw new Error(
      'MathMazePuzzle components must be used within MathMazePuzzleQuestion',
    );
  }
  return context;
};
