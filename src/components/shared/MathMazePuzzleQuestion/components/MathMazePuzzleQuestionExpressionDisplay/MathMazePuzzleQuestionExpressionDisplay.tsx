import React from 'react';
import { View, Text } from 'react-native';
import { useMathMazePuzzle } from '../../context';
import { styles } from './MathMazePuzzleQuestionExpressionDisplay.style';

const MathMazePuzzleQuestionExpressionDisplay: React.FC = () => {
  const { state } = useMathMazePuzzle();
  const { expression } = state;

  return null; // intended 
};

export default MathMazePuzzleQuestionExpressionDisplay;