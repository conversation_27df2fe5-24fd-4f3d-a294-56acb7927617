import { StyleSheet } from 'react-native';
import theme from 'core/constants/themes/dark';

export const styles = StyleSheet.create({
  actionsContainer: {
    flexDirection: 'row',
    gap: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 15,
  },
  actionButton: {
    width: 50,
    height: 40,
  },
  actionButtonWide: {
    width: 90,
    height: 40,
  },
  actionButtonMedium: {
    width: 80,
    height: 40,
  },
  actionButtonLabel: {
    fontSize: 11,
    color: theme.colors.textLight,
  },
  actionButtonDisabled: {
    opacity: 0.5,
  }
});