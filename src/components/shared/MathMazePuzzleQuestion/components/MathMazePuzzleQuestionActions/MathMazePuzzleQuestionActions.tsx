import React, { useCallback } from 'react';
import { View } from 'react-native';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from 'atoms/Icon';
import { useMathMazePuzzle } from '../../context';
import { styles } from './MathMazePuzzleQuestionActions.style';
import theme from 'core/constants/themes/dark';

const ACTION_CONFIGS = {
  UNDO: {
    iconConfig: {
      name: 'undo-variant',
      type: ICON_TYPES.MATERIAL_COMMUNITY_ICONS,
      color: theme.colors.textLight,
      size: 20,
    },
  },
  REDO: {
    iconConfig: {
      name: 'redo-variant',
      type: ICON_TYPES.MATERIAL_COMMUNITY_ICONS,
      color: theme.colors.textLight,
      size: 20,
    },
  },
  RESET: {
    iconConfig: {
      name: 'refresh',
      type: ICON_TYPES.MATERIAL_ICONS,
      color: theme.colors.textLight,
      size: 22,
    },
    label: 'RESET',
  },
  // SOLVE: {
  //   iconConfig: {
  //     name: 'lightbulb-on-outline',
  //     type: ICON_TYPES.MATERIAL_COMMUNITY_ICONS,
  //     color: theme.colors.textLight,
  //     size: 22,
  //   },
  //   label: 'SOLVE',
  // },
  // GENERATE: {
  //   iconConfig: {
  //     name: 'dice-5-outline',
  //     type: ICON_TYPES.MATERIAL_COMMUNITY_ICONS,
  //     color: theme.colors.textLight,
  //     size: 22,
  //   },
  //   label: 'NEW',
  // },
};


const MathMazePuzzleQuestionActions: React.FC = () => {
  const {  onAction } = useMathMazePuzzle();

  const handleUndo = useCallback(() => onAction({ type: 'UNDO' }), [onAction]);
  const handleRedo = useCallback(() => onAction({ type: 'REDO' }), [onAction]);
  const handleReset = useCallback(() => onAction({ type: 'RESET_PATH' }), [onAction]);
  const handleSolve = useCallback(() => onAction({ type: 'AUTO_SOLVE' }), [onAction]);
  const handleGenerate = useCallback(() => onAction({ type: 'GENERATE_NEW_PUZZLE' }), [onAction]);

  return (
    <View style={styles.actionsContainer}>
      <InteractiveSecondaryButton
        onPress={handleUndo}
        iconConfig={ACTION_CONFIGS.UNDO.iconConfig}
        buttonContainerStyle={styles.actionButton}
      />
      <InteractiveSecondaryButton
        label={ACTION_CONFIGS.RESET.label}
        labelStyle={styles.actionButtonLabel}
        onPress={handleReset}
        iconConfig={ACTION_CONFIGS.RESET.iconConfig}
        buttonContainerStyle={styles.actionButtonWide}
      />
      {/* <InteractiveSecondaryButton
        label={ACTION_CONFIGS.SOLVE.label}
        labelStyle={styles.actionButtonLabel}
        onPress={handleSolve}
        iconConfig={ACTION_CONFIGS.SOLVE.iconConfig}
        buttonContainerStyle={styles.actionButtonWide}
      /> */}
      <InteractiveSecondaryButton
        onPress={handleRedo}
        iconConfig={ACTION_CONFIGS.REDO.iconConfig}
        buttonContainerStyle={styles.actionButton}
      />
      {/* <InteractiveSecondaryButton
        label={ACTION_CONFIGS.GENERATE.label}
        labelStyle={styles.actionButtonLabel}
        onPress={handleGenerate}
        iconConfig={ACTION_CONFIGS.GENERATE.iconConfig}
        buttonContainerStyle={styles.actionButtonMedium}
      /> */}
    </View>
  );
};

export default React.memo(MathMazePuzzleQuestionActions);