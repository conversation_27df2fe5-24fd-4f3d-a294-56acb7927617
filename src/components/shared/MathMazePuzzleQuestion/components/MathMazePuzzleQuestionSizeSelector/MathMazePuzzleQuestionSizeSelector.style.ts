import { StyleSheet } from 'react-native';
import theme from 'core/constants/themes/dark';

export const styles = StyleSheet.create({
  sizeSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
    paddingHorizontal: 10,
  },
  sizeSelectorLabel: {
    fontSize: 16,
    marginRight: 10,
    color: theme.colors.text,
  },
  sizeButtons: {
    flexDirection: 'row',
  },
  sizeButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.tertiary,
    marginHorizontal: 4,
  },
  sizeButtonSelected: {
    backgroundColor: theme.colors.secondary,
    borderColor: theme.colors.secondary,
  },
  sizeButtonText: {
    fontSize: 14,
    color: theme.colors.text,
  },
  sizeButtonTextSelected: {
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
});