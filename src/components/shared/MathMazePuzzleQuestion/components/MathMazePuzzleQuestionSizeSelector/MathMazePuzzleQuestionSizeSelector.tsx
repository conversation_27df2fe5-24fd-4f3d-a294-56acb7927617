import React from 'react';
import { View, Text, Pressable } from 'react-native';
import { styles } from './MathMazePuzzleQuestionSizeSelector.style';
import { GRID_SIZES } from '../../constants/puzzleConstants';
import { useMathMazePuzzle } from '../../context';

const MathMazePuzzleQuestionSizeSelector: React.FC = () => {
  const { state, onAction } = useMathMazePuzzle();
  const currentSize = state.gridSize;

  const handleSizeChange = (size: number) => {
    onAction({ type: 'CHANGE_GRID_SIZE', payload: { newSize: size } });
  };

  return (
    <View style={styles.sizeSelector}>
      <Text style={styles.sizeSelectorLabel}>Grid Size:</Text>
      <View style={styles.sizeButtons}>
        {GRID_SIZES.map(size => (
          <Pressable
            key={size}
            onPress={() => handleSizeChange(size)}
            style={[
              styles.sizeButton,
              currentSize === size && styles.sizeButtonSelected,
            ]}
          >
            <Text style={[
              styles.sizeButtonText,
              currentSize === size && styles.sizeButtonTextSelected,
            ]}>
              {size}x{size}
            </Text>
          </Pressable>
        ))}
      </View>
    </View>
  );
};

export default MathMazePuzzleQuestionSizeSelector;