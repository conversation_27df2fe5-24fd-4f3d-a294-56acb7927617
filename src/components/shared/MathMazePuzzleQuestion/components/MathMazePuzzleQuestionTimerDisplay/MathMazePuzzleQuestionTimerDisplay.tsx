import React, { useEffect, useState } from 'react';
import { Text, View } from 'react-native';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import theme from 'core/constants/themes/dark';
import _toNumber from 'lodash/toNumber';
import { useMathMazePuzzle } from '../../context';
import { styles } from './MathMazePuzzleQuestionTimerDisplay.style';

const MathMazePuzzleQuestionTimerDisplay: React.FC = () => {
  const { state } = useMathMazePuzzle();
  const { startTime } = state;

  const [seconds, setSeconds] = useState(0);

  useEffect(() => {
    const intervalId = setInterval(() => {
      setSeconds(
        Math.max(
          0,
          Math.floor((getCurrentTime() - _toNumber(startTime)) / 1000),
        ),
      );
    }, 1000);
    return () => clearInterval(intervalId);
  }, []);

  const formatTime = (totalSeconds: number): string => {
    const minutes = Math.floor(totalSeconds / 60);
    const secs = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <View style={styles.container}>
      <MaterialIcons
        name="timer"
        color={theme.colors.timerColor || theme.colors.secondary}
        size={18}
      />
      <Text style={styles.time}>{formatTime(seconds)}</Text>
    </View>
  );
};

export default React.memo(MathMazePuzzleQuestionTimerDisplay);
