import { StyleSheet } from 'react-native';
import theme from 'core/constants/themes/dark';

export const styles = StyleSheet.create({
  gridContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  gridRow: {
    flexDirection: 'row',
  },
  svgOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 18,
    color: theme.colors.text,
  },
});