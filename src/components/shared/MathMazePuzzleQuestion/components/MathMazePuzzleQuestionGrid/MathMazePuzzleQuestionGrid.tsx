import React, { useRef, useEffect } from 'react';
import { View, PanResponder, Platform, Text } from 'react-native';
import Svg, { Line, Circle } from 'react-native-svg';
import AnimatedTile from '../MathMazePuzzleQuestionAnimatedTile';
import { useMathMazePuzzle } from '../../context';
import { styles } from './MathMazePuzzleQuestionGrid.style';
import { LINE_CONFIG, TILE_CONFIG } from '../../constants/puzzleConstants';
import theme from 'core/constants/themes/dark';

const getTileFromGridPosition = (
  localX: number,
  localY: number,
  tileSize: number,
  gridSize: number
): { row: number; col: number } | null => {
  const cellDimension = tileSize + TILE_CONFIG.MARGIN * 2;

  const col = Math.floor(localX / cellDimension);
  const row = Math.floor(localY / cellDimension);

  if (row >= 0 && row < gridSize && col >= 0 && col < gridSize) {
    const xInCell = localX - col * cellDimension;
    const yInCell = localY - row * cellDimension;

    if (
      xInCell >= TILE_CONFIG.MARGIN &&
      xInCell < TILE_CONFIG.MARGIN + tileSize &&
      yInCell >= TILE_CONFIG.MARGIN &&
      yInCell < TILE_CONFIG.MARGIN + tileSize
    ) {
      return { row, col };
    }
  }
  return null;
};

const MathMazePuzzleQuestionGrid: React.FC = () => {
  const { state, onAction } = useMathMazePuzzle();
  const { puzzle, path, tileSize, gridSize, pathConnections } = state;

  const containerRef = useRef<View>(null);
  const containerScreenOffset = useRef({ x: 0, y: 0 });

  const panResponderInstance = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: (_evt, _gestureState) => Platform.OS !== 'web',
      onMoveShouldSetPanResponder: (_evt, _gestureState) => Platform.OS !== 'web',
      onPanResponderGrant: (evt) => {
        if (Platform.OS === 'web') return;
        onAction({ type: 'SET_IS_DRAGGING', payload: { isDragging: true } });
        const { pageX, pageY } = evt.nativeEvent;
        const localX = pageX - containerScreenOffset.current.x;
        const localY = pageY - containerScreenOffset.current.y;
        const tile = getTileFromGridPosition(localX, localY, tileSize, gridSize);
        if (tile) {
          const key = `${tile.row}-${tile.col}`;
           if (path.length === 0 || path[path.length -1] !== key) {
            onAction({ type: 'ADD_TO_PATH', payload: tile });
           }
        }
      },
      onPanResponderMove: (evt) => {
        if (Platform.OS === 'web') return;
        const { pageX, pageY } = evt.nativeEvent;
        const localX = pageX - containerScreenOffset.current.x;
        const localY = pageY - containerScreenOffset.current.y;
        const currentTileInfo = getTileFromGridPosition(localX, localY, tileSize, gridSize);
        if (currentTileInfo) {
          const currentTileKey = `${currentTileInfo.row}-${currentTileInfo.col}`;
          if (path.length === 0 || path[path.length - 1] !== currentTileKey) {
            onAction({ type: 'ADD_TO_PATH', payload: currentTileInfo });
          }
        }
      },
      onPanResponderRelease: () => {
        if (Platform.OS === 'web') return;
        onAction({ type: 'SET_IS_DRAGGING', payload: { isDragging: false } });
      },
      onPanResponderTerminate: () => {
        if (Platform.OS === 'web') return;
        onAction({ type: 'SET_IS_DRAGGING', payload: { isDragging: false } });
      },
    })
  ).current;

  const onContainerLayout = () => {
    if (containerRef.current) {
      containerRef.current.measure((_x, _y, _width, _height, absoluteX, absoluteY) => {
        if (typeof absoluteX === 'number' && typeof absoluteY === 'number') {
          containerScreenOffset.current = { x: absoluteX, y: absoluteY };
        }
      });
    }
  };

  const handleMouseDown = (row: number, col: number) => {
    if (Platform.OS === 'web') {
      onAction({ type: 'SET_IS_DRAGGING', payload: { isDragging: true } });
      onAction({ type: 'ADD_TO_PATH', payload: { row, col } });
    }
  };

  const handleMouseUp = () => {
    if (Platform.OS === 'web') {
      onAction({ type: 'SET_IS_DRAGGING', payload: { isDragging: false } });
    }
  };

  const handleMouseEnter = (row: number, col: number) => {
    if (Platform.OS === 'web' && state.isDragging) {
      const currentTileKey = `${row}-${col}`;
      const lastTileKey = state.path.length > 0 ? state.path[state.path.length - 1] : null;

      if (currentTileKey === lastTileKey) {
        return;
      }

      if (state.path.length >= 2) {
        const secondLastTileKey = state.path[state.path.length - 2];
        if (currentTileKey === secondLastTileKey) {
          onAction({ type: 'UNDO' });
          return;
        }
      }

      onAction({ type: 'ADD_TO_PATH', payload: { row, col } });
    }
  };

  useEffect(() => {
    if (Platform.OS === 'web') {
      const globalMouseUpListener = () => {
        if (state.isDragging) {
          onAction({ type: 'SET_IS_DRAGGING', payload: { isDragging: false } });
        }
      };
      document.addEventListener('mouseup', globalMouseUpListener);
      return () => {
        document.removeEventListener('mouseup', globalMouseUpListener);
      };
    }
  }, [Platform.OS, state.isDragging, onAction]);


  if (!puzzle) return <View style={styles.loadingContainer}><Text style={styles.loadingText}>Loading puzzle...</Text></View>;

  const isSelected = (row: number, col: number): boolean => path.includes(`${row}-${col}`);

  return (
    <View
      style={styles.gridContainer}
      {...(Platform.OS !== 'web' ? panResponderInstance.panHandlers : {})}
      ref={containerRef}
      onLayout={onContainerLayout}
    >
      <Svg style={styles.svgOverlay} width="100%" height="100%" pointerEvents="none">
        {pathConnections.map((connection, index) => {
          const [fromRow, fromCol] = connection.from;
          const [toRow, toCol] = connection.to;
          const fromX = fromCol * (tileSize + TILE_CONFIG.MARGIN * 2) + tileSize / 2 + TILE_CONFIG.MARGIN;
          const fromY = fromRow * (tileSize + TILE_CONFIG.MARGIN * 2) + tileSize / 2 + TILE_CONFIG.MARGIN;
          const toX = toCol * (tileSize + TILE_CONFIG.MARGIN * 2) + tileSize / 2 + TILE_CONFIG.MARGIN;
          const toY = toRow * (tileSize + TILE_CONFIG.MARGIN * 2) + tileSize / 2 + TILE_CONFIG.MARGIN;
          return (
            <Line
              key={`line-${index}`}
              x1={fromX} y1={fromY} x2={toX} y2={toY}
              stroke={'#a6a5f2'}
              strokeWidth={tileSize * LINE_CONFIG.STROKE_WIDTH_MULTIPLIER}
              strokeLinecap="round"
              opacity={LINE_CONFIG.OPACITY}
            />
          );
        })}
        {path.map((pathKey, index) => {
          const [row, col] = pathKey.split('-').map(Number);
          const centerX = col * (tileSize + TILE_CONFIG.MARGIN * 2) + tileSize / 2 + TILE_CONFIG.MARGIN;
          const centerY = row * (tileSize + TILE_CONFIG.MARGIN * 2) + tileSize / 2 + TILE_CONFIG.MARGIN;
          return (
            <Circle
              key={`circle-${index}`}
              cx={centerX} cy={centerY}
              r={tileSize * LINE_CONFIG.CIRCLE_RADIUS_MULTIPLIER}
              fill={theme.colors.secondary} 
            />
          );
        })}
      </Svg>

      {puzzle.grid.map((rowItems, rowIndex) => (
        <View key={`row-${rowIndex}`} style={styles.gridRow}>
          {rowItems.map((cell, colIndex) => {
            const key = `${rowIndex}-${colIndex}`;
            const isStart = rowIndex === 0 && colIndex === 0;
            const isEnd = rowIndex === gridSize - 1 && colIndex === gridSize - 1;
            const selected = isSelected(rowIndex, colIndex);
            
            let borderColor = theme.colors.border;
            if (selected) {
              borderColor = theme.colors.selectedGridBorderColor || theme.colors.secondary;
            } else if (isStart) {
              borderColor = theme.colors.green;
            } else if (isEnd) {
              borderColor = theme.colors.red;
            } else {
              borderColor = (rowIndex + colIndex) % 2 === 0 ? theme.colors.border : theme.colors.tertiary;
            }

            const tileInteractionProps = Platform.OS === 'web' ? {
              onMouseDown: () => handleMouseDown(rowIndex, colIndex),
              onMouseEnter: () => handleMouseEnter(rowIndex, colIndex),
              onMouseUp: handleMouseUp,
            } : {};

            return (
              <AnimatedTile
                key={key}
                row={rowIndex}
                col={colIndex}
                cell={cell}
                tileSize={tileSize}
                borderColor={borderColor}
                isSelected={selected}
                onPress={() => {
                  if (!state.isDragging) {
                     onAction({ type: 'ADD_TO_PATH', payload: { row: rowIndex, col: colIndex } });
                  }
                }}
                tileProps={tileInteractionProps}
              />
            );
          })}
        </View>
      ))}
    </View>
  );
};

export default MathMazePuzzleQuestionGrid;
