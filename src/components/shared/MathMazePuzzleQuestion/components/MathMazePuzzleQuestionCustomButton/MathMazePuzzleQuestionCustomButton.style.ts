import { StyleSheet } from 'react-native';
import theme from 'core/constants/themes/dark';

export const styles = StyleSheet.create({
  customButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 5,
  },
  customButtonText: {
    color: theme.colors.textLight,
    fontSize: 16,
    fontFamily: 'Montserrat-600',
  },
  customButtonDisabled: {
    backgroundColor: theme.colors.tertiary,
  },
  customButtonTextDisabled: {
    color: theme.colors.textDark,
  },
});