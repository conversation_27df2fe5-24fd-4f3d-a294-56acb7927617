import { StyleSheet } from 'react-native';
import theme from 'core/constants/themes/dark';

export const styles = StyleSheet.create({
  container: {
    height: 38,
    backgroundColor: theme.colors.tertiary,
    borderColor: '#a6a5f2',
    borderWidth: 0.5,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 6,
    flexDirection: 'row',
    minWidth: 100,
  },
  targetText: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 1,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Montserrat-700',
    color: '#a6a5f2',
  },
});