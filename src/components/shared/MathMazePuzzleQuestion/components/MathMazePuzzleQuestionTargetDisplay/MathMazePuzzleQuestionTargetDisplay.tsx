import React, { useEffect } from 'react';
import { Text, View } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSequence,
  withTiming,
  withSpring,
} from 'react-native-reanimated';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useMathMazePuzzle } from '../../context';
import { styles } from './MathMazePuzzleQuestionTargetDisplay.style';
import theme from 'core/constants/themes/dark';

const MathMazePuzzleQuestionTargetDisplay: React.FC = () => {
  const { state } = useMathMazePuzzle();
  const { target } = state;

  const targetScale = useSharedValue(1);

  useEffect(() => {
    targetScale.value = withSequence(
      withTiming(1.2, { duration: 200 }),
      withSpring(1, { damping: 8, stiffness: 100 })
    );
  }, [target, targetScale]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: targetScale.value }],
  }));

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <MaterialIcons name="flag" color={theme.colors.timerColor || theme.colors.secondary} size={18} />
      <Text style={styles.targetText}>{target ?? '...'}</Text>
    </Animated.View>
  );
};

export default MathMazePuzzleQuestionTargetDisplay;