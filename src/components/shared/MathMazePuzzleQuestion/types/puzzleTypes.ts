export interface PuzzleGrid {
  grid: string[][];
  result: number;
  solutionPath: [number, number][];
}

export interface PathConnection {
  from: [number, number];
  to: [number, number];
}

export interface HistoryEntry {
  path: string[];
  expression: string;
}

export interface TileLayout {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface TileConfig {
  SPACING_DIVISOR: number;
  MAX_SIZE: number;
  LINE_WIDTH_MULTIPLIER: number;
  CIRCLE_RADIUS_MULTIPLIER: number;
  MARGIN: number;
}

export interface LineConfig {
  STROKE_WIDTH_MULTIPLIER: number;
  OPACITY: number;
  CIRCLE_RADIUS_MULTIPLIER: number;
}

export interface PuzzleColors {
  normalText: string;
  selectedText: string;
  selectedTileBackground: string;
  line: string;
  selected: string;
  startTile: string;
  endTile: string;
  default1: string;
  default2: string;
}

export interface MathMazePuzzleQuestionProps {
  puzzleData?: PuzzleGrid | string;
  initialGridSize?: number;
  onSubmit?: (result: { timeSpent: number; expression: string; path: string[] }) => void;
  onWin?: (result: { timeSpent: number; expression: string; path: string[] }) => void;
  onLose?: (result: { timeSpent: number; expression: string; path: string[] }) => void;
  children: React.ReactNode;
  shouldCacheTime?: boolean;
  shouldCacheGrid?: boolean;
}