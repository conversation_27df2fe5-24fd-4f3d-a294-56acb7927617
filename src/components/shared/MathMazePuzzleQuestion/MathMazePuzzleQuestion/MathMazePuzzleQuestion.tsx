import React from 'react';
import { MathMazePuzzleQuestionProps } from '../types';
import { MathMazePuzzleContext } from '../context';
import useMathMazePuzzleContextState from '../hooks/useMathMazePuzzleContextState';
import {
  Actions,
  ExpressionDisplay,
  Grid,
  SizeSelector,
  TargetDisplay,
  TimerDisplay,
} from '../components';
const MathMazePuzzleQuestionRoot: React.FC<MathMazePuzzleQuestionProps> = ({
  children,
  initialGridSize,
  puzzleData,
  onSubmit,
  onWin,
  onLose,
}) => {
  const value = useMathMazePuzzleContextState({
    initialGridSize,
    puzzleData,
    onSubmit,
    onWin,
    onLose,
  });

  return (
    <MathMazePuzzleContext.Provider value={value}>
      {children}
    </MathMazePuzzleContext.Provider>
  );
};

const MathMazePuzzleQuestion = Object.assign(MathMazePuzzleQuestionRoot, {
  Grid,
  Actions,
  Timer: TimerDisplay,
  Target: TargetDisplay,
  Expression: ExpressionDisplay,
  SizeSelector,
});

export default MathMazePuzzleQuestion;