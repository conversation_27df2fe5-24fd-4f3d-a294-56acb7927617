import React, { useCallback, useMemo } from 'react';
import { View } from 'react-native';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import _map from 'lodash/map';
import { KEYBOARD_TYPE, KEYBOARD_TYPES } from './constants';
import styles from './CustomKeyboard.style';
import KeyboardKey from './KeyboardKey';
import { CustomKeyboardProps, UserSettingsKeyboardType } from './types';
import { useUserSettings } from '@/src/core/contexts/UserSettingsContext';

const CustomKeyboard: React.FC<CustomKeyboardProps> = (props) => {
  const {
    onKeyPress,
    onDelete,
    customKeyboardType = KEYBOARD_TYPES.NUMBERS,
  } = props;

  const { userSettings } = useUserSettings();
  const { keyboardType } = userSettings;

  const { ABILITY_KEYS, KEYS } = useMemo(() => {
    if (keyboardType === UserSettingsKeyboardType.TELEPHONE) {
      return KEYBOARD_TYPE.TELEPHONE;
    } else if (keyboardType === UserSettingsKeyboardType.CALCULATOR) {
      return KEYBOARD_TYPE.CALCULATOR;
    }
    return KEYBOARD_TYPE.TELEPHONE;
  }, [keyboardType]);

  const { keys, keyWidth } = useMemo(() => {
    const keysArray =
      customKeyboardType === KEYBOARD_TYPES.NUMBERS_AND_OPERATORS
        ? ABILITY_KEYS
        : KEYS;
    const width =
      customKeyboardType === KEYBOARD_TYPES.NUMBERS_AND_OPERATORS
        ? '24%'
        : '32%';
    return { keys: keysArray, keyWidth: width };
  }, [customKeyboardType, keyboardType]);

  const renderDeleteIcon = useCallback(
    () => <FontAwesome6 name="delete-left" size={20} color="white" />,
    [],
  );

  const getKeyPressHandler = useCallback(
    (key: string) => () => onKeyPress(key),
    [onKeyPress],
  );

  return (
    <View style={styles.container}>
      <View style={styles.keyRow}>
        {_map(keys, (key) => (
          <KeyboardKey
            key={key}
            onPress={getKeyPressHandler(key)}
            label={key}
            keyWidth={keyWidth}
          />
        ))}
        <KeyboardKey
          key="delete"
          onPress={onDelete}
          renderLabel={renderDeleteIcon}
          keyWidth={keyWidth}
        />
      </View>
    </View>
  );
};

export default React.memo(CustomKeyboard);
