import { useLocalSearchParams } from 'expo-router';
import React, { useEffect } from 'react';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { View } from 'react-native';
import ErrorView from 'atoms/ErrorView';
import { checkIsValidDate } from 'modules/puzzles/utils/puzzleUtils';
import ErrorBoundary from 'atoms/ErrorBoundary';
import MathMazePuzzle from 'modules/puzzles/pages/MathMazePuzzle/MathMazePuzzle';
import MathMazePuzzleHeader from 'modules/puzzles/components/MathMazePuzzleHeader';

const TRACKED_ATTEMPT_FOR_DATE = {};

const MathMazePuzzleContainer = () => {
  const { date } = useLocalSearchParams();

  const isValidDate = checkIsValidDate(date);

  useEffect(() => {
    if (TRACKED_ATTEMPT_FOR_DATE[date]) return;
    TRACKED_ATTEMPT_FOR_DATE[date] = true;
    Analytics.track(
      ANALYTICS_EVENTS.MATH_MAZE_PUZZLE.ATTEMPTING_MATH_MAZE_PUZZLE,
      {
        date,
      },
    );
  }, [date]);

  if (!isValidDate) {
    return (
      <View style={{ flex: 1 }}>
        <MathMazePuzzleHeader />
        <ErrorView errorMessage="Sorry, Puzzle is not available for selected date" />
      </View>
    );
  }

  return (
    <ErrorBoundary componentName="MathMazePuzzleContainer">
      <MathMazePuzzle date={date} />
    </ErrorBoundary>
  );
};

MathMazePuzzleContainer.displayName = 'MathMazePuzzleContainer';

export default React.memo(MathMazePuzzleContainer);
