import { ScrollView, View } from 'react-native';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import usePrevious from 'core/hooks/usePrevious';
import { router } from 'expo-router';
import ACTIVITY_TYPES from 'core/constants/activityTypes';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import useUserActivityTracker from 'core/hooks/useUserActivityTracker';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import puzzleReader from 'modules/puzzles/readers/puzzleReader';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import MathMazePuzzleQuestion from 'shared/MathMazePuzzleQuestion/MathMazePuzzleQuestion';
import Loading from 'atoms/Loading';
import ErrorView from 'atoms/ErrorView';
import useGetDailyPuzzle from '../../hooks/queries/useGetDailyPuzzle';
import useSubmitPuzzleSolution from '../../hooks/mutations/useSubmitPuzzleSolution';
import { getMathMazePuzzleData } from '../../utils/getMathMazePuzzleFromString';

const MathMazePuzzle = React.memo(({ date }: { date: string | string[] }) => {
  const { isMobile: isCompactMode } = useMediaQuery();

  const { puzzle, loading, error } = useGetDailyPuzzle({
    date,
    puzzleType: PUZZLE_TYPES.MATH_MAZE,
  });

  const mathMazeSpecifics = puzzleReader.mathMazePuzzleString(puzzle);

  const mathMazeData = useMemo(() => {
    if (mathMazeSpecifics) {
      return getMathMazePuzzleData(mathMazeSpecifics);
    }
    return null;
  }, [mathMazeSpecifics]);

  const { updateActivity } = useUserActivityTracker();
  const { submitPuzzleSolution } = useSubmitPuzzleSolution({
    puzzleId: puzzleReader.id(puzzle),
    puzzleDate: date,
    puzzleType: PUZZLE_TYPES.MATH_MAZE,
  });

  const hasSolved = puzzleReader.hasAttempted(puzzle);
  const puzzleResult = puzzleReader.currentUserResult(puzzle);
  const previousPuzzleResult = usePrevious(puzzleResult);
  const previousPuzzleResultRef = useRef(previousPuzzleResult);
  previousPuzzleResultRef.current = previousPuzzleResult;

  const onSubmitPuzzle = useCallback(
    ({
      timeSpent,
    }: {
      timeSpent: number;
      expression: string;
      path: string[];
    }) => {
      if (hasSolved) return;
      updateActivity({
        activityType: ACTIVITY_TYPES.DAILY_PUZZLE,
        duration: timeSpent,
      });

      Analytics.track(
        ANALYTICS_EVENTS.MATH_MAZE_PUZZLE.SOLVED_MATH_MAZE_PUZZLE,
        {
          timeSpent,
          date,
        },
      );
      submitPuzzleSolution({ timeSpent })
        .then(() =>
          router.replace(
            `/puzzle/daily-challenge/${date}/result?puzzleType=${PUZZLE_TYPES.MATH_MAZE}`,
          ),
        )
        .catch((e) => {
          Analytics.track(
            ANALYTICS_EVENTS.MATH_MAZE_PUZZLE
              .ERROR_WHILE_SUBMITTING_MATH_MAZE_PUZZLE,
            { error: e },
          );
          showToast({
            type: TOAST_TYPE.ERROR,
            description: 'Something went wrong while submitting the puzzle',
          });
        });
    },
    [hasSolved, updateActivity, date, submitPuzzleSolution],
  );

  useEffect(() => {
    if (hasSolved && !_isEmpty(puzzleResult)) {
      router.replace(
        `/puzzle/daily-challenge/${date}/result?puzzleType=${PUZZLE_TYPES.MATH_MAZE}`,
      );
    }
  }, [date, hasSolved, puzzleResult]);

  if (loading || _isNil(puzzle)) {
    return <Loading label="Loading Puzzle..." />;
  }

  if (error) {
    return (
      <ErrorView errorMessage="Something went wrong while loading puzzle" />
    );
  }

  if (!mathMazeData) {
    return <ErrorView errorMessage="Failed to parse puzzle data." />;
  }

  return (
    <View style={{ flex: 1 }}>
      <MathMazePuzzleQuestion
        puzzleData={mathMazeData}
        onSubmit={onSubmitPuzzle}
        shouldCacheTime
        initialGridSize={mathMazeData.grid.length || 5}
      >
        <View
          style={{ alignItems: 'center', paddingVertical: 10, width: '100%' }}
        >
          <View
            style={{
              flexDirection: isCompactMode ? 'column' : 'row',
              justifyContent: 'space-around',
              width: '90%',
              alignItems: 'center',
              gap: 10,
            }}
          >
            <MathMazePuzzleQuestion.Timer />
            <MathMazePuzzleQuestion.Target />
          </View>
          <MathMazePuzzleQuestion.Expression />
        </View>
        <ScrollView
          contentContainerStyle={{
            flexGrow: 1,
            padding: 8,
            gap: isCompactMode ? 16 : 32,
            justifyContent: 'flex-start',
            alignItems: 'center',
          }}
          showsVerticalScrollIndicator={false}
        >
          <MathMazePuzzleQuestion.Grid />
          <MathMazePuzzleQuestion.Actions />
        </ScrollView>
      </MathMazePuzzleQuestion>
    </View>
  );
});

export default MathMazePuzzle;
