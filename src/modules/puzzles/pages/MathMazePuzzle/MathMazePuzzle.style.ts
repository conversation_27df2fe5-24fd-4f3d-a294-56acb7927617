import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  headerCenterContentContainer: {
    flex: 1,
    gap: 2,
    alignItems: 'center',
  },
  puzzleLevel: {
    fontFamily: 'Montserrat-400',
    fontSize: 12,
    color: 'white',
  },
  puzzleNumber: {
    fontFamily: 'Montserrat-600',
    fontSize: 16,
    color: 'white',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 18,
    width: '100%',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
  },
  headerTitle: {
    fontSize: 17,
    fontFamily: 'Montserrat-500',
    color: 'white',
    alignSelf: 'center',
  },
});

export default styles;
