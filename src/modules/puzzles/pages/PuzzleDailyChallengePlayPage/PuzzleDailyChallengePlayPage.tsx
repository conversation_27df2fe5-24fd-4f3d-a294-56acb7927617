import React from 'react';
import CrossMathPuzzleContainer from 'modules/puzzles/pages/CrossMathPuzzle/CrossMathPuzzleContainer';
import KenKenPuzzleContainer from 'modules/puzzles/pages/KenKenPuzzle/KenKenPuzzleContainer';
import { useLocalSearchParams } from 'expo-router';
import _isNil from 'lodash/isNil';
import HectocPuzzleContainer from 'modules/puzzles/pages/HectocPuzzle/HectocPuzzleContainer';
import MathMazePuzzleContainer from 'modules/puzzles/pages/MathMazePuzzle/MathMazePuzzleContainer';
import { PUZZLE_TYPES } from '../../types/puzzleType';

const COMPONENT_FACTORY = {
  [PUZZLE_TYPES.CROSS_MATH_PUZZLE]: CrossMathPuzzleContainer,
  [PUZZLE_TYPES.KEN_KEN_PUZZLE]: KenKenPuzzleContainer,
  [PUZZLE_TYPES.HECTOC_PUZZLE]: HectocPuzzleContainer,
  [PUZZLE_TYPES.MATH_MAZE]: MathMazePuzzleContainer,
};

const PuzzleDailyChallengePlayPage = () => {
  const { puzzleType }: { puzzleType: string } = useLocalSearchParams();

  const Component = COMPONENT_FACTORY[puzzleType];

  if (_isNil(Component)) {
    return <CrossMathPuzzleContainer />;
  }

  return <Component />;
};

export default React.memo(PuzzleDailyChallengePlayPage);
