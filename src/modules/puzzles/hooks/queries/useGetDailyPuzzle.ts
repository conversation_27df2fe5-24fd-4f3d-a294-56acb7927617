import {
  ApolloError,
  gql,
  useQuery,
  WatchQueryFetchPolicy,
} from '@apollo/client';
import _get from 'lodash/get';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';

export const GET_DAILY_PUZZLE_QUERY = gql`
  query GetDailyPuzzleByType($date: String!, $puzzleType: PuzzleType!) {
    getDailyPuzzleByType(date: $date, puzzleType: $puzzleType) {
      id
      difficulty
      solvedBy
      cells {
        isVisible
        value
        type
      }
      puzzleType
      puzzleDate
      availableAnswers
      typeSpecific {
        puzzleType
        kenKen {
          puzzleString
        }
        crossMath {
          puzzleString
        }
        hectoc {
          puzzleString
        }
        mathMaze {
          puzzleString
        }
      }
      hasAttempted
      currentUserResult {
        id
        userId
        puzzleId
        timeSpent
        completedAt
        statikCoinsEarned
        puzzleDate
        puzzleType
      }
      stats {
        numOfSubmission
        averageTime
        bestTime
      }
      userStat {
        id
        userId
        bestTime
        averageTime
        numOfSubmission
        puzzleType
      }
    }
  }
`;

interface GetDailyPuzzleResponse {
  puzzle: any;
  loading: boolean;
  error: ApolloError | undefined;
}

const useGetDailyPuzzle = ({
  date,
  puzzleType = PUZZLE_TYPES.CROSS_MATH_PUZZLE,
  fetchPolicy = 'cache-first',
}: {
  date: string | string[];
  fetchPolicy?: WatchQueryFetchPolicy;
  puzzleType?: string;
}): GetDailyPuzzleResponse => {
  const { data, loading, error } = useQuery(GET_DAILY_PUZZLE_QUERY, {
    fetchPolicy,
    variables: {
      date,
      puzzleType,
    },
    notifyOnNetworkStatusChange: true,
  });

  return {
    puzzle: _get(data, 'getDailyPuzzleByType'),
    loading,
    error,
  };
};

export default useGetDailyPuzzle;
