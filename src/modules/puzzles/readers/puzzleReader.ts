import _get from 'lodash/get';
import {
  PuzzleStatsType,
  PuzzleType,
  UserResultType,
  UserStatType,
} from '../types/puzzleType';

const puzzleReader = {
  id: (puzzle: PuzzleType) => _get(puzzle, 'id'),
  name: (puzzle: PuzzleType) => _get(puzzle, 'name'),
  difficulty: (puzzle: PuzzleType) => _get(puzzle, 'difficulty'),
  solvedBy: (puzzle: PuzzleType) => _get(puzzle, 'solvedBy', 0),
  cells: (puzzle: PuzzleType) => _get(puzzle, 'cells'),
  puzzleDate: (puzzle: PuzzleType) => _get(puzzle, 'puzzleDate'),
  availableAnswers: (puzzle: PuzzleType) =>
    _get(puzzle, 'availableAnswers', ['']),
  hasAttempted: (puzzle: PuzzleType): boolean =>
    _get(puzzle, 'hasAttempted', false),
  currentUserResult: (puzzle: PuzzleType): UserResultType | null =>
    _get(puzzle, 'currentUserResult'),
  stats: (puzzle: PuzzleType): PuzzleStatsType | null => _get(puzzle, 'stats'),
  userStat: (puzzle: PuzzleType): UserStatType | null =>
    _get(puzzle, 'userStat'),

  kenKenPuzzleString: (puzzle: PuzzleType): string =>
    _get(puzzle, ['typeSpecific', 'kenKen', 'puzzleString'], ''),
  hectocPuzzleString: (puzzle: PuzzleType): string =>
    _get(puzzle, ['typeSpecific', 'hectoc', 'puzzleString'], ''),
  mathMazePuzzleString: (puzzle: PuzzleType) =>
    _get(puzzle, ['typeSpecific', 'mathMaze', 'puzzleString'], ''),
  crossMathPuzzleString: (puzzle: PuzzleType) =>
    _get(puzzle, ['typeSpecific', 'crossMath', 'puzzleString'], ''),
};

export default puzzleReader;
