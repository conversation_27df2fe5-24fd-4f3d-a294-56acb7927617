export const PUZZLE_TYPES = {
  CROSS_MATH_PUZZLE: 'CrossMath',
  K<PERSON>_KEN_PUZZLE: 'KenKen',
  HECTOC_PUZZLE: 'Hectoc',
  MATH_MAZE: 'MathMaze',
};

export const VALID_PUZZLE_TYPES = [
  PUZZLE_TYPES.CROSS_MATH_PUZZLE,
  PUZZLE_TYPES.KEN_KEN_PUZZLE,
  PUZZLE_TYPES.MATH_MAZE,
];

export interface PuzzleCellType {
  id: string;
  value: string;
  type: string;
  isVisible: boolean;
}

export interface UserResultType {
  id: string;
  userId: string;
  puzzleId: string;
  timeSpent: number;
  completedAt: string;
  statikCoinsEarned: number;
  puzzleDate: string;
}

export interface PuzzleStatsType {
  numOfSubmission: number;
  averageTime: number;
  bestTime: number;
}

export interface UserStatType {
  id: string;
  userId: string;
  numOfSubmission?: number;
  averageTime?: number;
  bestTime?: number;
}

export interface PuzzleTypeSpecific {
  kenKen?: {
    puzzleString: string;
  };
  crossMath?: {
    puzzleString: string;
  };
  mathMaze?: {
    grid: string[][];
    result: number;
    solutionPath: [number, number][];
    puzzleString: string;
  };
}

export interface PuzzleType {
  id: string;
  name: string;
  difficulty: number;
  solvedBy: string;
  cells: PuzzleCellType[];
  puzzleDate: string;
  availableAnswers: string[];
  hasAttempted: boolean;
  currentUserResult: UserResultType | null;
  stats: PuzzleStatsType | null;
  userStat: UserStatType | null;
  typeSpecific: PuzzleTypeSpecific;
}

export interface GetDailyPuzzleData {
  getDailyPuzzle: PuzzleType;
}

export interface GetDailyPuzzleVars {
  date: string;
}
