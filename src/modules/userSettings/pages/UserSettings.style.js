import { StyleSheet } from 'react-native';

import APP_LAYOUT_CONSTANTS from 'core/constants/appLayout';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
    maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
    gap: 20,
  },
  playSoundContanier: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 10,
    width: '100%',
    height: 50,
    backgroundColor: dark.colors.gradientBackground,
    borderRadius: 12,
    paddingHorizontal: 15,
    marginTop: 10,
  },
  dropdown:{
    flexShrink: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 10,
    width: '100%',
    maxWidth: 150,
    borderRadius: 12,
    paddingHorizontal: 15,
  },
  dropDownContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 50,
    marginVertical: 10,
    borderRadius: 12,
    paddingLeft: 15,
    paddingRight: 10,
    backgroundColor: dark.colors.gradientBackground,
    width: '100%',
  },
  settingName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: dark.colors.textDark,
  },
  compactScrollContainer: {
    marginHorizontal: 20,
    gap: 10,
  },
  switch: {
    marginLeft: 'auto',
  },
});

export default styles;
