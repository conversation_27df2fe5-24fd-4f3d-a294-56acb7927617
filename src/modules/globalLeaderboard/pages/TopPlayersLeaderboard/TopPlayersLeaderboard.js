import React, { useCallback, useEffect, useRef, useState } from 'react';
import _map from 'lodash/map';
import { ScrollView, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Header from 'shared/Header';
import { useRouter } from 'expo-router';
import TabButtonWithGradient from 'shared/TabButtonWithGradient';
import useIsAddCollegeFeatureAvailable from '@/src/hooks/features/useIsAddCollegeFeatureAvailable';
import TopPlayersCategoryLeaderboard from '../../components/TopPlayersCategoryLeaderboard';
import styles from './TopPlayersLeaderboard.style';
import {
  LEADERBOARD_TYPES,
  TAB_INDICES,
  TABS,
} from '../../constants/leaderboardConstants';

const LeaderboardPageContainer = () => {
  const { isMobile } = useMediaQuery();
  const [activeTab, setActiveTab] = useState(TAB_INDICES.FRIENDS);
  const router = useRouter();
  const scrollViewRef = useRef(null);
  const tabRefs = useRef([]);

  const [tabsList, setTabsList] = useState(TABS);
  const [scrollViewWidth, setScrollViewWidth] = useState(0);

  const isAddCollegeFeatureAvailable = useIsAddCollegeFeatureAvailable();

  useEffect(() => {
    if (!isAddCollegeFeatureAvailable) {
      setTabsList(
        TABS.filter((tab) => tab.label !== LEADERBOARD_TYPES.INSTITUTE),
      );
    }
  }, [isAddCollegeFeatureAvailable]);

  useEffect(() => {
    tabRefs.current = tabRefs.current.slice(0, tabsList.length);
  }, [tabsList]);

  const handleTabPress = useCallback(
    (index) => {
      setActiveTab(index);
      router.setParams({
        tab: tabsList[index].label,
      });

      const activeTabLayout = tabRefs.current[index]?.layout;
      if (activeTabLayout && scrollViewRef.current && scrollViewWidth > 0) {
        const tabCenterX = activeTabLayout.x + activeTabLayout.width / 2;
        let scrollToX = tabCenterX - scrollViewWidth / 2;
        scrollToX = Math.max(0, scrollToX);
        scrollViewRef.current.scrollTo({ x: scrollToX, animated: true });
      }
    },
    [router, scrollViewWidth, tabsList],
  );

  return (
    <View style={[styles.mainContainer, !isMobile && styles.expandedContainer]}>
      <Header title="Leaderboard" />
      <View style={styles.container}>
        <View style={styles.tabContainer}>
          <View style={styles.tabHeaderScrollContainer}>
            <ScrollView
              ref={scrollViewRef}
              contentContainerStyle={styles.tabHeaderContainer}
              showsHorizontalScrollIndicator={false}
              horizontal
              onLayout={(event) => {
                setScrollViewWidth(event.nativeEvent.layout.width);
              }}
            >
              {_map(tabsList, (tab, index) => (
                <View
                  key={index}
                  ref={(el) => {
                    if (tabRefs.current) {
                      tabRefs.current[index] = el;
                    }
                  }}
                  onLayout={(event) => {
                    if (tabRefs.current && tabRefs.current[index]) {
                      tabRefs.current[index].layout = event.nativeEvent.layout;
                    }
                  }}
                >
                  <TabButtonWithGradient
                    isActive={activeTab === index}
                    onPress={() => handleTabPress(index)}
                    iconName={tab.iconName}
                    label={tab.label}
                  />
                </View>
              ))}
            </ScrollView>
          </View>

          <TopPlayersCategoryLeaderboard type={TABS[activeTab].label} />
        </View>
      </View>
    </View>
  );
};

export default React.memo(LeaderboardPageContainer);
