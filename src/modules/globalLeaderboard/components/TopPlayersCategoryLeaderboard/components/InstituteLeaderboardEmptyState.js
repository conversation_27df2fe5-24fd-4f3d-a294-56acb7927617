import React from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import dark from 'core/constants/themes/dark';

const noDataImage = require('assets/images/pookie/no_friends_pookie.png');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: dark.colors.background,
  },
  image: {
    width: 96,
    height: 78,
    marginBottom: 8,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Montserrat-600',
    color: dark.colors.text,
    textAlign: 'center',
    marginBottom: 10,
  },
  subText: {
    fontSize: 14,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
    textAlign: 'center',
    marginBottom: 20,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: dark.colors.tertiary,
    borderWidth: 1,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 20,
  },
  buttonText: {
    fontSize: 14,
    fontFamily: 'Montserrat-500',
    color: dark.colors.secondary,
    marginLeft: 8,
  },
});

const InstituteLeaderboardEmptyState = ({ onInviteFriends }) => (
  <View style={styles.container}>
    <Image source={noDataImage} style={styles.image} />
    <Text style={styles.title}>It's Quiet in Here...</Text>
    <Text style={styles.subText}>
      No players yet on your institute's leaderboard. Be the first to shine or
      invite more friends to join the competition!
    </Text>
    <TouchableOpacity style={styles.button} onPress={onInviteFriends}>
      <MaterialIcons
        name="person-add"
        size={18}
        color={dark.colors.secondary}
      />
      <Text style={styles.buttonText}>Invite Friends</Text>
    </TouchableOpacity>
  </View>
);

export default React.memo(InstituteLeaderboardEmptyState);
