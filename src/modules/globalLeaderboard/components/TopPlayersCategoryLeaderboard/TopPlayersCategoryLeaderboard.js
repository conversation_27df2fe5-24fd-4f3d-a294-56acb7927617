import React, { useCallback, useEffect, useState } from 'react';
import { Image, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import noFriendsPookie from '@/assets/images/pookie/no_friends_pookie.png';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import dark from '@/src/core/constants/themes/dark';
import { router, useRouter } from 'expo-router';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import useUsersInstituteTopPlayers from 'modules/globalLeaderboard/hooks/useUsersInstituteTopLeaderboard';
import userReader from 'core/readers/userReader';
import _isNil from 'lodash/isNil';
import useInviteFriendOnMatiks from 'core/hooks/useInviteFriendOnMatiks';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import Analytics from 'core/analytics';
import AddCollegeEmptyState from 'modules/globalLeaderboard/components/TopPlayersCategoryLeaderboard/components/AddCollegeEmptyState';
import InstituteLeaderboardEmptyState from 'modules/globalLeaderboard/components/TopPlayersCategoryLeaderboard/components/InstituteLeaderboardEmptyState';
import _map from 'lodash/map';
import TopPlayersLeaderboardCard from './components/TopPlayersLeaderboardCard';
import styles from './TopPlayersCategoryLeaderboard.style';
import useGlobalTopPlayers from '../../hooks/useGlobalTopPlayers';
import useFriendsTopPlayers from '../../hooks/useFriendsTopPlayers';
import LEADERBOARD_CATEGORIES, {
  LEADERBOARD_TYPES,
} from '../../constants/leaderboardConstants';

const TopPlayersCategoryUI = ({ topPlayers, loading, type, onAddFriends }) => {
  const renderCategoryCard = useCallback(
    (category, index) => {
      const { id, title, icon, ratingKey } = category;
      const players = topPlayers[ratingKey] || EMPTY_ARRAY;
      const isLast = index === LEADERBOARD_CATEGORIES.length - 1;

      return (
        <TopPlayersLeaderboardCard
          key={id}
          title={title}
          type={type}
          icon={icon}
          topPlayers={players}
          loading={loading}
          isLast={isLast}
        />
      );
    },
    [topPlayers, loading, type],
  );

  const { user } = useSession();
  const institutionId = userReader.institutionId(user);
  const { handleNormalShare } = useInviteFriendOnMatiks();

  const handleAddInstitute = useCallback(() => {
    if (_isNil(institutionId)) {
      Analytics.track(
        ANALYTICS_EVENTS.LEADERBOARD.CLICKED_ON_ADD_INSTITUTE_COLLEGE_TAB,
      );
      router.push(`/profile/${userReader.username(user)}/add-college-friends`);
      return;
    }
    handleNormalShare?.({
      eventToBeTracked:
        ANALYTICS_EVENTS.LEADERBOARD.CLICKED_ON_INVITE_FRIEND_COLLEGE_TAB,
    });
  }, [handleNormalShare, institutionId, user]);

  if (
    type === LEADERBOARD_TYPES.FRIENDS &&
    !loading &&
    Object.values(topPlayers).every((arr) => arr.length === 0)
  ) {
    return (
      <View style={styles.emptyStateContainer}>
        <Image source={noFriendsPookie} style={styles.pookieImage} />
        <Text style={styles.emptyStateText}>NO FRIENDS TO SHOW</Text>
        <Text style={styles.emptyStateSubText}>
          Add friends to challenge, compete, and see where you rank in your
          social circle!
        </Text>
        <TouchableOpacity
          style={styles.addFriendsButton}
          onPress={onAddFriends}
        >
          <MaterialIcons name="add" size={16} color={dark.colors.secondary} />
          <Text style={styles.addFriendsButtonText}>Add friends</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (
    type === LEADERBOARD_TYPES.INSTITUTE &&
    !loading &&
    Object.values(topPlayers).every((arr) => arr.length === 0)
  ) {
    if (_isNil(institutionId)) {
      return <AddCollegeEmptyState onAddCollege={handleAddInstitute} />;
    }
    return (
      <InstituteLeaderboardEmptyState onInviteFriends={handleAddInstitute} />
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {_map(LEADERBOARD_CATEGORIES, (category, index) =>
        renderCategoryCard(category, index),
      )}
    </ScrollView>
  );
};

const TopPlayersCategoryContainer = ({ type }) => {
  const [topPlayers, setTopPlayers] = useState({
    globalRating: EMPTY_ARRAY,
    memoryRating: EMPTY_ARRAY,
    abilityRating: EMPTY_ARRAY,
    puzzleRating: EMPTY_ARRAY,
  });
  const router = useRouter();
  const { user } = useSession();

  const { data: globalData, loading: globalLoading } = useGlobalTopPlayers();
  const { data: friendsData, loading: friendsLoading } = useFriendsTopPlayers();
  const { data: instituteData, loading: instituteLoading } =
    useUsersInstituteTopPlayers();

  const loading =
    type === LEADERBOARD_TYPES.GLOBAL
      ? globalLoading
      : type === LEADERBOARD_TYPES.INSTITUTE
        ? instituteLoading
        : friendsLoading;
  const data =
    type === LEADERBOARD_TYPES.GLOBAL
      ? globalData
      : type === LEADERBOARD_TYPES.INSTITUTE
        ? instituteData
        : friendsData;

  useEffect(() => {
    if (data) {
      const newTopPlayers = {
        globalRating: data.globalRating || EMPTY_ARRAY,
        memoryRating: data.memoryRating || EMPTY_ARRAY,
        abilityRating: data.abilityRating || EMPTY_ARRAY,
        puzzleRating: data.puzzleRating || EMPTY_ARRAY,
      };
      setTopPlayers(newTopPlayers);
    }
  }, [data]);

  const handleAddFriends = useCallback(() => {
    router.push(`/profile/${user?.username}/friends`);
  }, [router, user]);

  return (
    <TopPlayersCategoryUI
      key={type}
      topPlayers={topPlayers}
      loading={loading}
      type={type}
      onAddFriends={handleAddFriends}
    />
  );
};

export default React.memo(TopPlayersCategoryContainer);
