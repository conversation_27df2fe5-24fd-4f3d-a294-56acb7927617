import React, { useCallback, useMemo } from 'react';
import {
  Linking,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import _isNil from 'lodash/isNil';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import _map from 'lodash/map';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import Icon, { ICON_TYPES } from 'atoms/Icon';
import userReader from '@/src/core/readers/userReader';
import { router, useLocalSearchParams } from 'expo-router';
import { useSession } from 'modules/auth/containers/AuthProvider';
import EditProfilePage from 'modules/profile/pages/EditProfilePage';
import { showRightPane } from 'molecules/RightPane/RightPane';
import useIsAddCollegeFeatureAvailable from '@/src/hooks/features/useIsAddCollegeFeatureAvailable';
import styles from './SocialLinks.style';
import { getSocialIcons } from './utils';

const ADD_COLLEGE_ROUTE = 'add-college';
const ADD_SOCIAL_ROUTE = 'edit-profile';

interface SocialLinksProps {
  user: any;
}

const SocialLinks: React.FC<SocialLinksProps> = ({ user }) => {
  const isAddCollegeFeatureAvailable = useIsAddCollegeFeatureAvailable();
  const { isMobile: isCompactMode } = useMediaQuery();
  const links = userReader.links(user);

  const { user: currentSessionUser } = useSession();
  const { username } = useLocalSearchParams();

  const isCurrentSessionUser = useMemo(
    () => _isEqual(userReader.username(currentSessionUser), username),
    [currentSessionUser, username],
  );

  const isGuestUser = useMemo(() => userReader.isGuest(user), [user]);

  const userInstitution = useMemo(
    () => userReader.institutionName(user),
    [user],
  );

  const socialData = useMemo(() => getSocialIcons({ links }), [links]);
  const hasSocialLinks = useMemo(
    () => !_isEmpty(socialData) && !_isNil(socialData),
    [socialData],
  );

  const containerStyle = useMemo(
    () => [{ gap: 15, paddingTop: 20 }, !isCompactMode && styles.mainContainer],
    [isCompactMode],
  );

  const scrollViewContentStyle = useMemo(
    () => [styles.container, isCompactMode && { gap: 8 }],
    [isCompactMode],
  );

  const onPressSocialLink = useCallback(({ item } = EMPTY_OBJECT) => {
    Analytics.track(ANALYTICS_EVENTS.PROFILE.CLICKED_ON_SOCIAL_LINK, {
      platform: item?.platform,
    });
    if (item?.url) {
      Linking.openURL(item.url).catch((err) => {
        console.warn('Failed to open URL:', err);
      });
    }
  }, []);

  const handleAddCollegePress = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.PROFILE.CLICKED_ADD_COLLEGE);
    router.push(`/profile/${userReader.username(user)}/${ADD_COLLEGE_ROUTE}`);
  }, [user]);

  const handleAddSocialPress = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.PROFILE.CLICKED_ADD_SOCIAL);
    if (isCompactMode) {
      router.push(`/profile/${userReader.username(user)}/${ADD_SOCIAL_ROUTE}`);
      return;
    }
    showRightPane({
      content: <EditProfilePage />,
    });
  }, [isCompactMode, user]);

  const handleCollegePress = useCallback(() => {
    if (!isCurrentSessionUser) {
      return;
    }
    Analytics.track(ANALYTICS_EVENTS.PROFILE.CLICKED_COLLEGE_LINK);
    if (userInstitution) {
      router.push(
        `/profile/${userReader.username(user)}/add-college?instituteName=${userInstitution}`,
      );
    }
  }, [user, userInstitution, isCurrentSessionUser]);

  const renderIcon = useCallback(
    (item: any) => (
      <View style={styles.iconContainer}>
        {' '}
        <Icon size={12} color="white" {...item.iconConfig} />
      </View>
    ),
    [],
  );

  const renderCollegeButton = useCallback(() => {
    if (!isAddCollegeFeatureAvailable) {
      return null;
    }
    if (!_isNil(userInstitution)) {
      return (
        <TouchableOpacity style={styles.linkItem} onPress={handleCollegePress}>
          <View style={styles.iconContainer}>
            <Icon
              name="school-outline"
              type={ICON_TYPES.IONICON}
              size={12}
              color="white"
            />
          </View>

          <Text style={styles.linkText} ellipsizeMode="tail" numberOfLines={1}>
            {userInstitution || 'My College'}
          </Text>
        </TouchableOpacity>
      );
    }
    if (isCurrentSessionUser) {
      return (
        <TouchableOpacity
          style={styles.linkItem}
          onPress={handleAddCollegePress}
        >
          <View style={styles.iconContainer}>
            <Icon
              name="plus"
              size={12}
              color="white"
              type={ICON_TYPES.ANT_DESIGN}
            />
          </View>
          <Text style={styles.linkText}>Add College</Text>
        </TouchableOpacity>
      );
    }
    return null;
  }, [
    userInstitution,
    isCurrentSessionUser,
    handleCollegePress,
    handleAddCollegePress,
    isAddCollegeFeatureAvailable,
  ]);

  const renderAddSocialsButton = useCallback(() => {
    if (isCurrentSessionUser) {
      return (
        <TouchableOpacity
          style={styles.linkItem}
          onPress={handleAddSocialPress}
        >
          <View style={styles.iconContainer}>
            <Icon
              name="plus"
              size={12}
              color="white"
              type={ICON_TYPES.ANT_DESIGN as any}
            />
          </View>
          <Text style={styles.linkText}>Add Socials</Text>
        </TouchableOpacity>
      );
    }
    return null;
  }, [isCurrentSessionUser, handleAddSocialPress]);

  const renderSocialLinks = useCallback(() => {
    if (!hasSocialLinks) return null;

    return _map(socialData, (item) => (
      <TouchableOpacity
        key={item?.platform}
        style={styles.linkItem}
        onPress={() => onPressSocialLink({ item })}
      >
        {renderIcon(item)}
        <Text style={styles.linkText} ellipsizeMode="tail" numberOfLines={1}>
          {`${item?.username}`}
        </Text>
      </TouchableOpacity>
    ));
  }, [hasSocialLinks, socialData, onPressSocialLink, renderIcon]);

  const SocialLinksContainer = useCallback(
    ({ children }: { children: React.ReactNode }) => (
      <View style={containerStyle}>
        <ScrollView
          horizontal
          style={{ width: '100%' }}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={scrollViewContentStyle}
        >
          {children}
        </ScrollView>
      </View>
    ),
    [containerStyle, scrollViewContentStyle],
  );

  const shouldShowContent =
    userInstitution || hasSocialLinks || isCurrentSessionUser;

  if (!shouldShowContent || isGuestUser) {
    return null;
  }

  return (
    <SocialLinksContainer>
      {renderCollegeButton()}
      {renderSocialLinks()}
      {!hasSocialLinks && renderAddSocialsButton()}
    </SocialLinksContainer>
  );
};

export default React.memo(SocialLinks);
