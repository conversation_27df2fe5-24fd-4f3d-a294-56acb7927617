import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

export default StyleSheet.create({
  container: {
    paddingVertical: 8,
    // paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  infoContainer: {
    flex: 1,
    marginLeft: 12,
    marginRight: 10,
  },
  logoContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  nameText: {
    fontFamily: 'Montserrat-600',
    fontSize: 13,
    color: dark.colors.text,
    marginBottom: 4,
  },
  locationContainer: {
    marginTop: 2,
  },
  locationText: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
  },
});
