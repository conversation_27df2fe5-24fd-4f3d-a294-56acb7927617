import React from 'react';
import { Image, Pressable, Text, View } from 'react-native';
import Icon, { ICON_TYPES } from '@/src/components/atoms/Icon';
import DefaultCollegeIcon from '@/src/components/atoms/DefaultCollegeIcon';
import dark from '@/src/core/constants/themes/dark';
import styles from './InstitutionListItem.style';

interface InstitutionListItemProps {
  institution: any;
  onPress: (institution: any) => void;
  isSelected: boolean;
}

const InstitutionListItem = ({
  institution,
  onPress,
  isSelected,
}: InstitutionListItemProps) => {
  if (!institution) {
    return null;
  }

  const handlePress = () => {
    if (onPress) {
      onPress(institution);
    }
  };

  return (
    <Pressable onPress={handlePress}>
      <View style={styles.container}>
        <View style={styles.logoContainer}>
          {institution?.logo ? (
            <Image source={{ uri: institution.logo }} style={styles.logo} />
          ) : (
            <DefaultCollegeIcon size={20} />
          )}
        </View>
        <View style={styles.infoContainer}>
          <Text style={styles.nameText}>{institution?.name}</Text>
          {(institution?.city ||
            institution?.state ||
            institution?.country) && (
            <View style={styles.locationContainer}>
              <Text style={styles.locationText}>
                {[institution?.city, institution?.state, institution?.country]
                  .filter(Boolean)
                  .join(', ')}
              </Text>
            </View>
          )}
        </View>
        {isSelected && (
          <Icon
            name="check"
            size={20}
            color={dark.colors.secondary}
            type={ICON_TYPES.ANT_DESIGN}
          />
        )}
      </View>
    </Pressable>
  );
};

export default React.memo(InstitutionListItem);
