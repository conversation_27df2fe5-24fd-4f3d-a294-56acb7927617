import _findIndex from 'lodash/findIndex';
import _get from 'lodash/get';
import { PLAYER_STATUS } from 'modules/game/constants/game';

const shouldJoinGame = ({ currentUserId, players }: any) => {
  const currentUserIndex = _findIndex(
    players,
    (player) =>
      _get(player, 'userId') === currentUserId &&
      _get(player, 'status') === PLAYER_STATUS.ACCEPTED,
  );

  const isCurrentUserInGame = currentUserIndex !== -1;

  return !isCurrentUserInGame;
};

export default shouldJoinGame;
