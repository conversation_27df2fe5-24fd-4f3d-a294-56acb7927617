import { useCallback, useMemo } from 'react';
import _isEqual from 'lodash/isEqual';
import { usePathname } from 'expo-router';
import { getCurrentActiveGameId } from 'core/utils/getUserCurrentActivity';
import { closePopover, showPopover } from 'molecules/Popover/Popover';
import AFKDetected from 'shared/AFKDetected';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _find from 'lodash/find';
import gameLeaderboardReader from 'core/readers/gameLeaderboardReader';
import { useSession } from 'modules/auth/containers/AuthProvider';

let RESPONDING_TO_STILL_IN_GAME_EVENT = false;
const DEFAULT_WAIT_TIME = 7;

const useHandleStillInGameEvent = ({
  respondToStillInGame,
}: {
  respondToStillInGame: Function;
}) => {
  const { userId } = useSession();
  const currentUrl = usePathname();

  const { isMobile: isCompactMode } = useMediaQuery();

  const currActiveGameId = useMemo(
    () => getCurrentActiveGameId({ currentUrl }),
    [currentUrl],
  );

  const handleOnBackdropPress = useCallback(async () => {
    if (RESPONDING_TO_STILL_IN_GAME_EVENT) {
      return;
    }
    RESPONDING_TO_STILL_IN_GAME_EVENT = true;
    await respondToStillInGame?.();
  }, [respondToStillInGame]);

  const handleStillInGameEvent = useCallback(
    ({ game }: { game: any }) => {
      const { _id: gameId } = game ?? EMPTY_OBJECT;

      const isOnSameGame = _isEqual(currActiveGameId, gameId);

      const leaderBoard = game?.leaderBoard || [];
      const currentUserEntry = _find(
        leaderBoard,
        (entry) => entry.userId === userId,
      );

      const correct = gameLeaderboardReader.correct(currentUserEntry) || 0;
      const incorrectAttempts =
        gameLeaderboardReader.incorrectAttempts(currentUserEntry) || 0;
      const totalPoints =
        gameLeaderboardReader.totalPoints(currentUserEntry) || 0;
      const incorrect = gameLeaderboardReader.incorrect(currentUserEntry) || 0;

      if (
        correct !== 0 ||
        incorrect !== 0 ||
        incorrectAttempts !== 0 ||
        totalPoints !== 0
      ) {
        respondToStillInGame?.();
        return;
      }

      if (isOnSameGame) {
        showPopover({
          content: <AFKDetected waitTime={DEFAULT_WAIT_TIME} />,
          overlayLook: false,
          animationType: 'fade',
          style: {
            position: 'absolute',
            width: '100%',
            top: isCompactMode ? 145 : 300,
          },
          onBackdropPress: () => {
            handleOnBackdropPress?.();
          },
        });
        setTimeout(() => {
          closePopover();
        }, DEFAULT_WAIT_TIME * 1000);
      }
    },
    [
      currActiveGameId,
      handleOnBackdropPress,
      isCompactMode,
      respondToStillInGame,
      userId,
    ],
  );

  return {
    handleStillInGameEvent,
  };
};

export default useHandleStillInGameEvent;
