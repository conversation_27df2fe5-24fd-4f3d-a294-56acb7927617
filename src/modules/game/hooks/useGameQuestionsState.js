import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import _find from 'lodash/find';
import _toString from 'lodash/toString';
import _isEmpty from 'lodash/isEmpty';
import _reduce from 'lodash/reduce';
import _filter from 'lodash/filter';
import _head from 'lodash/head';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import _values from 'lodash/values';
import _keys from 'lodash/keys';
import _sumBy from 'lodash/sumBy';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import { ABILITY_QUESTION_CATEGORY } from 'core/constants/questionCategories';
import useGameContext from './useGameContext';

import { GAME_TYPES } from '@/src/core/constants/gameTypes';

const useRunOnceOnCondition = (condition, callback, dependencyArray = []) => {
  const hasRun = useRef(false);
  useEffect(() => {
    if (condition && !hasRun.current) {
      callback();
      hasRun.current = true;
    }
  }, [...dependencyArray]);
};

const useGameQuestionsState = () => {
  const { game, submitAnswer } = useGameContext();
  const { user, userId } = useSession();
  const {
    questions: allQuestions = EMPTY_ARRAY,
    _id: gameId,
    leaderBoard = EMPTY_ARRAY,
    gameType,
  } = game;

  const initialValue = _reduce(
    allQuestions,
    (acc, questionObject) => {
      const { submissions, question } = questionObject;
      const submissionByCurrentUser = _filter(
        submissions,
        (submission) => submission?.userId === user?._id,
      );

      const { id } = question;
      acc[id] = {
        question,
        hasSolved: false,
        incorrectAttempts: 0,
      };

      if (!_isEmpty(submissionByCurrentUser)) {
        const { id } = question;
        acc[id].hasSolved = true;
      }
      return acc;
    },
    {},
  );

  useRunOnceOnCondition(
    (gameType === GAME_TYPES.DMAS || gameType === GAME_TYPES.PLAY_ONLINE) &&
      !_isEmpty(initialValue),
    () => {
      setQuestions(initialValue);
    },
    [initialValue, gameType],
  );

  const [questions, setQuestions] = useState(initialValue);
  const questionsRef = useRef(questions);
  questionsRef.current = questions;

  const updateQuestion = useCallback(
    ({ qid, key, value }) => {
      const prevQuestionState = questions[qid];
      if (_isEmpty(prevQuestionState)) {
        return;
      }
      setQuestions((prevState) => {
        const updatedState = {
          ...prevState,
          [qid]: {
            ...prevState[qid],
            [key]: value,
          },
        };
        return updatedState;
      });
    },
    [questions],
  );

  const [currentQuestionId, setCurrentQuestionId] = useState(
    _head(_keys(questions)),
  );

  const [solvedAllQuestions, setSolvedAllQuestions] = useState(false);

  const updateCurrentQuestion = useCallback(() => {
    const firstUnSolvedQuestion = _find(
      _values(questions),
      (questionObject) => !questionObject?.hasSolved,
    );

    if (_isEmpty(firstUnSolvedQuestion)) {
      setSolvedAllQuestions(true);
      return;
    }

    const { question } = firstUnSolvedQuestion;
    setCurrentQuestionId(question?.id);
  }, [setSolvedAllQuestions, questions]);

  const handleCorrectAnswerSubmitted = useCallback(
    ({ question, value }) => {
      const { id: questionId } = question;
      updateQuestion({
        qid: questionId,
        key: 'hasSolved',
        value: true,
      });
      const incorrectAttempts = questions[questionId]?.incorrectAttempts ?? 0;
      submitAnswer({
        gameId,
        questionId,
        submittedValue: _toString(value),
        timeOfSubmission: getCurrentTimeWithOffset(),
        isCorrect: true,
        incorrectAttempts,
        userId,
      });
    },
    [gameId, questions, updateQuestion, submitAnswer, userId],
  );

  const handleIncorrectAnswerSubmitted = useCallback(
    ({ question, value }) => {
      const { id: questionId } = question;
      const currentAttempts = questions[questionId]?.incorrectAttempts || 0;
      const updatedIncorrectAttempts = currentAttempts + 1;
      updateQuestion({
        qid: questionId,
        key: 'incorrectAttempts',
        value: updatedIncorrectAttempts,
      });
      submitAnswer({
        gameId,
        questionId,
        submittedValue: _toString(value),
        timeOfSubmission: getCurrentTimeWithOffset(),
        isCorrect: false,
        incorrectAttempts: updatedIncorrectAttempts,
        userId,
      });
    },
    [questions, updateQuestion, submitAnswer, gameId, userId],
  );

  const handleSubmitAnswer = useCallback(
    ({ questionId, value, isCheckedWithWasm, isCorrect: _isCorrect }) => {
      const questionObj = _find(_values(questions), (questionObj) => {
        return questionObj?.question?.id === questionId;
      });
      const { question } = questionObj;

      if (_isEmpty(question)) {
        return false;
      }
      const { answers, tags, expression } = question;
      const category = tags?.[0];

      let isCorrect = false;

      switch (category) {
        case ABILITY_QUESTION_CATEGORY.PRIME_FACTORIZATION:
          isCorrect =
            value.length === answers.length &&
            value.every(
              (factor, index) => _toString(factor) === answers[index],
            );
          break;

        case ABILITY_QUESTION_CATEGORY.SUM_OF_SQUARES:
          const sumOfSquares = _sumBy(value, (num) => num * num);
          const sumOfNumbers = _sumBy(value);

          const targetSum = Number(expression[0]);
          isCorrect = sumOfSquares === targetSum || targetSum === sumOfNumbers;
          break;

        default:
          isCorrect = answers?.[0] === _toString(value);
      }

      if (isCorrect || !!(isCheckedWithWasm && _isCorrect)) {
        handleCorrectAnswerSubmitted({
          question,
          value,
        });
        return true;
      }
      handleIncorrectAnswerSubmitted({
        question,
        value,
      });
      return false;
    },
    [questions, handleCorrectAnswerSubmitted, handleIncorrectAnswerSubmitted],
  );

  const playersScores = useMemo(() => {
    const scores = {};
    const currentPlayersLocalScore = _reduce(
      questions,
      (acc, questionObject) => {
        const { hasSolved } = questionObject;
        return acc + (hasSolved ? 1 : 0);
      },
      0,
    );

    for (let i = 0; i < leaderBoard?.length; i++) {
      scores[leaderBoard?.[i]?.userId] = leaderBoard?.[i]?.correct;
    }
    scores[userId] = Math.max(currentPlayersLocalScore, scores[userId]);
    return scores;
  }, [leaderBoard, questions, userId]);

  const updateCurrentQuestionRef = useRef(updateCurrentQuestion);
  updateCurrentQuestionRef.current = updateCurrentQuestion;

  useEffect(() => {
    updateCurrentQuestionRef.current();
  }, [questions]);

  useEffect(() => {
    if (_isEmpty(questionsRef.current) && !_isEmpty(initialValue)) {
      setQuestions(initialValue);
    }
  }, [initialValue]);

  return {
    currentQuestion: questions[currentQuestionId]?.question,
    submitAnswer: handleSubmitAnswer,
    playersScores,
    incorrectAttempts: questions[currentQuestionId]?.incorrectAttempts,
    solvedAllQuestions,
  };
};

export default useGameQuestionsState;
