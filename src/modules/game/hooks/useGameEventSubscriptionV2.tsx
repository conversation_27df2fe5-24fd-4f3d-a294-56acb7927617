import { useCallback, useEffect, useRef } from 'react';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import { decryptJsonData } from 'core/utils/encryptions/decrypt';
import { useUserActivityContext } from 'core/contexts/UserActivityContext';
import gameReader from '@/src/core/readers/gameReader';
import _includes from 'lodash/includes';
import { WEBSOCKET_CHANNELS } from 'core/constants/websocket';
import useWebsocketStore from 'store/useWebSocketStore';
import EventManager from '@/src/core/event';
import useGameStore from '@/src/store/useGameStore';
import { listenersNamespace } from '@/src/core/event/constants';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import { hideToast } from '@/src/components/molecules/Toast';
import _toNumber from 'lodash/toNumber';
import { GAME_EVENTS, GAME_STATUS } from '../constants/game';
import useUpdateCurrentUserOnGameEnd from './useUpdateCurrentUserOnGameEnd';
import useHandleGameEvents from './useHandleGameEvents';

const useGameEventSubscriptionV2 = ({ gameId }: { gameId: string }) => {
  const { updateUserOnGameEnd } = useUpdateCurrentUserOnGameEnd();
  const userActivityContextData = useUserActivityContext();
  const { updateActivity } = userActivityContextData ?? EMPTY_OBJECT;

  const updateUserOnGameEndRef = useRef(updateUserOnGameEnd);
  updateUserOnGameEndRef.current = updateUserOnGameEnd;

  const { currentGame, updateCurrentGame } = useGameStore((state) => ({
    currentGame: state.currentGame,
    updateCurrentGame: state.updateCurrentGame,
  }));

  const { isConnected, sendMessage, joinChannel, leaveChannel } =
    useWebsocketStore((state) => ({
      isConnected: state.isConnected,
      sendMessage: state.sendMessage,
      joinChannel: state.joinChannel,
      leaveChannel: state.leaveChannel,
    }));
  const channel = WEBSOCKET_CHANNELS.GameEvents(gameId);

  const submitAnswer = useCallback(
    (data: any) => {
      sendMessage({
        type: 'submitAnswer',
        channel,
        data: {
          ...data,
        },
      });
    },
    [channel, sendMessage],
  );

  const isActiveGame = !_includes(
    [GAME_STATUS.ENDED, GAME_STATUS.CANCELLED, GAME_STATUS.ABORTED],
    gameReader.gameStatus(currentGame),
  );

  const joinChannelRef = useRef(joinChannel);
  joinChannelRef.current = joinChannel;

  const leaveChannelRef = useRef(leaveChannel);
  leaveChannelRef.current = leaveChannel;

  const updateActivityRef = useRef(updateActivity);
  updateActivityRef.current = updateActivity;

  useEffect(() => {
    if (isConnected && isActiveGame) {
      joinChannelRef.current(channel);
    }
    if (!isActiveGame) {
      leaveChannelRef.current(channel);
    }
    return () => {
      leaveChannelRef.current(channel);
    };
  }, [isConnected, isActiveGame, channel]);

  const setGame = useCallback(
    (game: any) => {
      if (_isEmpty(game)) {
        return game;
      }

      const { encryptedQuestions } = game ?? EMPTY_OBJECT;

      if (_isNil(encryptedQuestions)) {
        return updateCurrentGame(game);
      }

      const questions = _map(encryptedQuestions, decryptJsonData);

      updateCurrentGame({ ...game, questions });
    },
    [updateCurrentGame],
  );

  const runOnceRef = useRef(false);

  const setGameRef = useRef(setGame);
  setGameRef.current = setGame;

  const onGameEvent = useCallback((data: any) => {
    const game = data?.game;
    const event = data?.event;
    const { config, gameType } = game ?? EMPTY_OBJECT;
    const { timeLimit, numPlayers } = config ?? EMPTY_OBJECT;
    setGameRef.current(game);

    switch (event) {
      case GAME_EVENTS.GAME_STARTED: {
        Analytics.track(ANALYTICS_EVENTS.GAME_STARTED, {
          gameType,
          timeLimit,
          numPlayers,
        });
        hideToast();
        break;
      }
      case GAME_EVENTS.GAME_ENDED: {
        Analytics.track(ANALYTICS_EVENTS.GAME_ENDED, {
          gameType,
          timeLimit,
          numPlayers,
        });
        updateUserOnGameEndRef.current({ game });
        updateActivityRef.current({
          activityType: `${gameType}`,
          duration: _toNumber(timeLimit) * 1000,
        });
        break;
      }
      default: {
        // do nothing
      }
    }
  }, []);

  const onGameEventRef = useRef(onGameEvent);
  onGameEventRef.current = onGameEvent;

  useEffect(() => {
    if (!gameId || runOnceRef.current) return;
    runOnceRef.current = true;
    const eventManager = new EventManager();
    eventManager.on(gameId, listenersNamespace.GameEvent, (data) => {
      onGameEventRef.current(data);
    });

    return () => {
      eventManager.off(gameId, listenersNamespace.GameEvent);
    };
  }, [gameId]);

  const { handleUserJoinEvent, handleRemovePlayerEvent } =
    useHandleGameEvents();

  const handleUserJoinEventRef = useRef(handleUserJoinEvent);
  handleUserJoinEventRef.current = handleUserJoinEvent;

  const handleRemovePlayerEventRef = useRef(handleRemovePlayerEvent);
  handleRemovePlayerEventRef.current = handleRemovePlayerEvent;

  return {
    submitAnswer,
  };
};

export default useGameEventSubscriptionV2;
