import { useCallback, useEffect, useMemo, useRef } from 'react';
import { hideToast } from 'molecules/Toast';
import _map from 'lodash/map';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _toNumber from 'lodash/toNumber';
import { decryptJsonData } from 'core/utils/encryptions/decrypt';
import { useUserActivityContext } from 'core/contexts/UserActivityContext';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import gameReader from '@/src/core/readers/gameReader';
import _includes from 'lodash/includes';
import useWebsocketStore from 'store/useWebSocketStore';
import { WEBSOCKET_CHANNELS } from 'core/services/WebSocketManager';
import useHandleStillInGameEvent from 'modules/game/hooks/useHandleStillInGameEvent';
import { useSession } from 'modules/auth/containers/AuthProvider';
import useHandleGameEvents from './useHandleGameEvents';
import useUpdateCurrentUserOnGameEnd from './useUpdateCurrentUserOnGameEnd';
import { GAME_EVENTS, GAME_STATUS } from '../constants/game';

const GAME_END_TRIGGERED_FOR_GAME_IDS: any = {};
const STILL_IN_GAME_TRIGGERED_FOR_GAME_IDS: any = {};
const ABORTED_GAME_TRIGGERED_FOR_GAME_IDS: any = {};

const useGameEventSubscription = ({ gameId }: { gameId: string }) => {
  const { userId } = useSession();
  const { updateUserOnGameEnd } = useUpdateCurrentUserOnGameEnd();
  const userActivityContextData = useUserActivityContext();
  const { updateActivity } = userActivityContextData ?? EMPTY_OBJECT;

  const updateUserOnGameEndRef = useRef(updateUserOnGameEnd);
  updateUserOnGameEndRef.current = updateUserOnGameEnd;

  const { lastMessage, sendMessage, joinChannel, leaveChannel } =
    useWebsocketStore((state) => ({
      lastMessage: state.lastMessage,
      sendMessage: state.sendMessage,
      joinChannel: state.joinChannel,
      leaveChannel: state.leaveChannel,
    }));
  const channel = WEBSOCKET_CHANNELS.GameEvents(gameId);

  const submitAnswer = useCallback(
    (data: any) => {
      sendMessage({
        type: 'submitAnswer',
        channel,
        data: {
          ...data,
        },
      });
    },
    [channel, sendMessage],
  );

  const respondToStillInGame = useCallback(() => {
    sendMessage({
      type: 'respondToStillInGame',
      data: {
        userId,
        gameId,
      },
    });
  }, [gameId, sendMessage, userId]);

  const { handleStillInGameEvent } = useHandleStillInGameEvent({
    respondToStillInGame,
  });

  const handleStillInGameEventRef = useRef(handleStillInGameEvent);
  handleStillInGameEventRef.current = handleStillInGameEvent;

  const { game, event } = useMemo(() => {
    const data = lastMessage?.[channel];
    const _game = data?.game;
    const _event = data?.event;
    return { event: _event, game: _game };
  }, [lastMessage?.[channel]]);

  const currentGameStatus = gameReader.gameStatus(game);

  const isActiveGame = !_includes(
    [GAME_STATUS.ENDED, GAME_STATUS.CANCELLED, GAME_STATUS.ABORTED],
    currentGameStatus,
  );

  const joinChannelRef = useRef(joinChannel);
  joinChannelRef.current = joinChannel;

  const leaveChannelRef = useRef(leaveChannel);
  leaveChannelRef.current = leaveChannel;

  useEffect(() => {
    if (isActiveGame) {
      joinChannelRef.current(channel);
    } else {
      leaveChannelRef.current(channel);
    }
    return () => {
      leaveChannelRef.current(channel);
    };
  }, [isActiveGame, channel]);

  const decryptedGame = useMemo(() => {
    if (_isEmpty(game)) {
      return game;
    }

    const { encryptedQuestions } = game ?? EMPTY_OBJECT;

    if (_isNil(encryptedQuestions)) {
      return game;
    }

    const questions = _map(encryptedQuestions, decryptJsonData);

    return { ...game, questions };
  }, [game]);

  const { handleUserJoinEvent, handleRemovePlayerEvent } =
    useHandleGameEvents();

  const handleUserJoinEventRef = useRef(handleUserJoinEvent);
  handleUserJoinEventRef.current = handleUserJoinEvent;

  const handleRemovePlayerEventRef = useRef(handleRemovePlayerEvent);
  handleRemovePlayerEventRef.current = handleRemovePlayerEvent;

  const updateActivityRef = useRef(updateActivity);
  updateActivityRef.current = updateActivity;

  const gameRef = useRef(game);
  gameRef.current = game;

  useEffect(() => {
    const { config, gameType } = gameRef.current ?? EMPTY_OBJECT;
    const { timeLimit, numPlayers } = config ?? EMPTY_OBJECT;

    switch (event) {
      case GAME_EVENTS.USER_JOINED: {
        handleUserJoinEventRef.current({ game: gameRef.current });
        break;
      }
      case GAME_EVENTS.GAME_STARTED: {
        Analytics.track(ANALYTICS_EVENTS.GAME_STARTED, {
          gameType,
          timeLimit,
          numPlayers,
        });
        hideToast();
        break;
      }
      case GAME_EVENTS.GAME_ENDED: {
        if (GAME_END_TRIGGERED_FOR_GAME_IDS[gameId]) {
          return;
        }
        GAME_END_TRIGGERED_FOR_GAME_IDS[gameId] = true;
        Analytics.track(ANALYTICS_EVENTS.GAME_ENDED, {
          gameType,
          timeLimit,
          numPlayers,
        });
        updateUserOnGameEndRef.current({ game: gameRef.current });
        updateActivityRef.current({
          activityType: `${gameType}`,
          duration: _toNumber(timeLimit) * 1000,
        });
        break;
      }
      case GAME_EVENTS.GAME_ABORTED: {
        if (ABORTED_GAME_TRIGGERED_FOR_GAME_IDS[gameId]) {
          return;
        }
        ABORTED_GAME_TRIGGERED_FOR_GAME_IDS[gameId] = true;
        Analytics.track(ANALYTICS_EVENTS.GAME_ABORTED, {
          gameType,
          timeLimit,
          numPlayers,
        });
        updateUserOnGameEndRef.current({ game: gameRef.current });
        break;
      }
      case GAME_EVENTS.USER_STILL_IN_GAME: {
        if (STILL_IN_GAME_TRIGGERED_FOR_GAME_IDS[gameId]) {
          return;
        }
        STILL_IN_GAME_TRIGGERED_FOR_GAME_IDS[gameId] = true;
        Analytics.track(ANALYTICS_EVENTS.ASKED_USER_STILL_IN_GAME, {
          gameType,
          timeLimit,
          numPlayers,
        });
        handleStillInGameEventRef.current({ game: gameRef.current });
        break;
      }
      case GAME_EVENTS.PLAYER_REMOVED: {
        handleRemovePlayerEventRef.current({ game: gameRef.current });
        break;
      }
      default: {
        // do nothing
      }
    }
  }, [event]);

  return {
    event,
    game: decryptedGame,
    submitAnswer,
  };
};

export default useGameEventSubscription;
