import { useCallback } from 'react';

import _find from 'lodash/find';
import _size from 'lodash/size';
import _isEmpty from 'lodash/isNil';
import _slice from 'lodash/slice';
import userReader from 'core/readers/userReader';
import _toNumber from 'lodash/toNumber';
import { useSession } from '../../auth/containers/AuthProvider';
import { GAME_TYPES } from '../../home/<USER>/gameTypes';

const rankUpdatedForGames = {};

const useUpdateCurrentUserOnGameEnd = () => {
  const { updateCurrentUser, userId: currentUserId, user } = useSession();

  const getCurrentUserStreak = useCallback(() => {
    if (_isEmpty(user)) {
      return;
    }

    const userStreaks = userReader.userStreaks(user) ?? EMPTY_OBJECT;

    const {
      currentStreak = 0,
      longestStreak = 0,
      lastSevenDays = [false, false, false, false, false, false, false],
    } = userStreaks ?? EMPTY_OBJECT;

    if (_size(lastSevenDays) < 7) {
      return;
    }

    if (lastSevenDays[6] === false) {
      lastSevenDays[6] = true;
      const updatedStreakObj = {
        currentStreak: currentStreak + 1,
        longestStreak: Math.max(currentStreak + 1, longestStreak),
        lastSevenDays: [..._slice(lastSevenDays, 0, 6), true],
      };
      return updatedStreakObj;
    }

    return userStreaks;
  }, [user]);

  const getUpdatedUserRating = useCallback(
    ({ game }) => {
      const { leaderBoard } = game;

      const currentPlayer = _find(
        leaderBoard,
        (player) => player.userId === currentUserId,
      );

      if (_isEmpty(currentPlayer)) return EMPTY_OBJECT;

      const { ratingChange = 0 } = currentPlayer ?? EMPTY_OBJECT;

      if (!ratingChange || ratingChange === 0) return;
      const updatedStats = { ...user?.stats, ngp: (user?.stats?.ngp ?? 0) + 1 };

      const userNewRating = _toNumber(userReader.rating(user)) + ratingChange;

      if (game?.gameType === GAME_TYPES.FLASH_ANZAN) {
        const flashAnzanRating = userReader.flashAnzanRating(user);
        return {
          ratingV2: {
            ...userReader.ratingV2(user),
            flashAnzanRating: flashAnzanRating + ratingChange,
          },
          stats: updatedStats,
        };
      }

      return {
        rating: userNewRating,
        ratingV2: {
          ...userReader.ratingV2(user),
          globalRating: userNewRating,
        },
        stats: updatedStats,
      };
    },
    [currentUserId, user],
  );

  const updateUserOnGameEnd = useCallback(
    ({ game }) => {
      if (_isEmpty(game)) {
        return;
      }

      const { _id: gameId } = game;
      if (rankUpdatedForGames[gameId]) return;

      const updatedUserStreakObj = getCurrentUserStreak();
      const userRatingAndStats = getUpdatedUserRating({ game });

      updateCurrentUser({
        ...user,
        ...userRatingAndStats,
        userStreaks: updatedUserStreakObj,
      });
      rankUpdatedForGames[gameId] = true;
    },
    [updateCurrentUser, getCurrentUserStreak, getUpdatedUserRating, user],
  );

  const updateUserStreak = useCallback(async () => {
    const updatedUserStreakObj = getCurrentUserStreak();
    await updateCurrentUser({ ...user, userStreaks: updatedUserStreakObj });
  }, [getCurrentUserStreak, updateCurrentUser, user]);

  return {
    updateUserOnGameEnd,
    updateUserStreak,
  };
};

export default useUpdateCurrentUserOnGameEnd;
