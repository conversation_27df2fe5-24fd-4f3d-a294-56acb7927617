import React, { useCallback, useEffect, useRef, useState } from 'react';
import _toNumber from 'lodash/toNumber';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import { Redirect } from 'expo-router';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import _isNil from 'lodash/isNil';
import gameReader from 'core/readers/gameReader';
import { GAME_STATUS } from '../../constants/game';

import ErrorView from '../../../../components/atoms/ErrorView';
import useGameContext from '../../hooks/useGameContext';
import Loading from '../../../../components/atoms/Loading';
import useEndGameQuery from '../../hooks/queries/useEndGameQuery';

const MAX_WAITING_TIME = 5000; // 5 seconds
const MAX_TIME_TO_END_GAME = 3000; // 3 seconds

const PlayGamePageWrapper = (props: any) => {
  const { children } = props;
  const { reFetchGame, game } = useGameContext();
  const startTime = gameReader.startTime(game);
  const gameStatus = gameReader.gameStatus(game);
  const gameType = gameReader.gameType(game);
  const gameId = gameReader.id(game);
  const startTimeDate = new Date(startTime);
  const timeLimit = gameReader.timeLimit(game);
  const { endGame } = useEndGameQuery();

  const currentTime = getCurrentTimeWithOffset();
  const endTime = startTimeDate.getTime() + _toNumber(timeLimit) * 1000;

  const fetchGameTimeoutRef = useRef();

  const [gameEndedForUser, setGameEndedForUser] = useState(
    currentTime > endTime,
  );

  const handleEndGame = useCallback(() => {
    if (gameStatus !== GAME_STATUS.ENDED) {
      endGame({ gameId });
    }
  }, [gameStatus, endGame, gameId]);

  const handleEndGameRef = useRef(handleEndGame);
  handleEndGameRef.current = handleEndGame;

  const endGameTimeoutRef = useRef();

  const reFetechGameRef = useRef(reFetchGame);
  reFetechGameRef.current = reFetchGame;

  useEffect(() => {
    const timeToGameEnd = endTime - getCurrentTimeWithOffset();

    const timeToReFetchGame = timeToGameEnd + MAX_WAITING_TIME;

    if (endTime > currentTime) {
      setTimeout(() => setGameEndedForUser(true), timeToGameEnd);

      endGameTimeoutRef.current = setTimeout(() => {
        Analytics.track(ANALYTICS_EVENTS.ENDING_GAME_FROM_CLIENT);
        handleEndGameRef.current();
      }, timeToGameEnd + MAX_TIME_TO_END_GAME);

      if (fetchGameTimeoutRef.current) {
        clearTimeout(fetchGameTimeoutRef.current);
      }
      // This is just for safe check, if user miss the game end event.
      fetchGameTimeoutRef.current = setTimeout(() => {
        reFetechGameRef.current();
      }, timeToReFetchGame);
    }

    return () => {
      clearTimeout(fetchGameTimeoutRef.current);
      clearTimeout(endGameTimeoutRef.current);
    };
  }, [endTime]);

  if (
    (endTime <= currentTime || gameEndedForUser) &&
    gameStatus === GAME_STATUS.STARTED
  ) {
    return <Loading label="Calculating result..." />;
  }

  if (gameStatus === GAME_STATUS.ENDED || gameStatus === GAME_STATUS.ABORTED) {
    return <Redirect href={`/game/${gameId}/result`} />;
  }

  if (gameStatus !== GAME_STATUS.STARTED) {
    return <ErrorView errorMessage="Something went wrong" />;
  }

  if (_isNil(children)) {
    return <ErrorView errorMessage="Invalid game type!" />;
  }

  return children;
};

export default React.memo(PlayGamePageWrapper);
