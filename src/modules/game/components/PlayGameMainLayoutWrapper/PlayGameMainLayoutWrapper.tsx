import React, { useEffect, useRef } from 'react';
import _size from 'lodash/size';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import _includes from 'lodash/includes';
import _map from 'lodash/map';
import userReader from 'core/readers/userReader';
import Loading from 'atoms/Loading';
import GameFullPage from 'modules/game/pages/GameFullPage';
import gameReader from '@/src/core/readers/gameReader';
import PlayGamePageWrapper from 'modules/game/components/PlayGamePageWrapper';
import { Redirect } from 'expo-router';
import shouldJoinGame from 'modules/game/utils/shouldJoinGame';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import useJoinGameQuery from '../../hooks/useJoinGameQuery';
import { GAME_MODES, GAME_STATUS } from '../../constants/game';
import useGameOwner from '../../hooks/useGameOwner';
import WaitingForFriend from '../../pages/PlayWithFriend/components/WaitingForFriend';
import useGameContext from '../../hooks/useGameContext';

const JOINED_GAME_CALLED: any = {};

const PlayGameMainLayoutWrapper = ({
  COMPONENT_FACTORY,
}: {
  COMPONENT_FACTORY: any;
}) => {
  const { game } = useGameContext();
  const { user, userId: currentUserId } = useSession();
  const { joinGame } = useJoinGameQuery();

  const { players, gameStatus, _id: gameId, config } = game;
  const gameMode = gameReader.gameMode(game);

  const { checkIsGameOwner } = useGameOwner();
  const isGameOwner = checkIsGameOwner({ game });

  const isUserInTheGame = _includes(
    _map(players, 'userId'),
    userReader.id(user),
  );

  const numPlayers = gameReader.numPlayers(game);

  const joinGameRef = useRef(joinGame);
  joinGameRef.current = joinGame;

  useEffect(() => {
    if (JOINED_GAME_CALLED[gameId]) return;

    JOINED_GAME_CALLED[gameId] = true;

    const shouldJoinTheGame = shouldJoinGame({
      currentUserId,
      players,
    });

    if (shouldJoinTheGame) {
      joinGameRef.current({ gameId }).then(() => {
        Analytics.track(ANALYTICS_EVENTS.JOIN_GAME, { gameId });
      });
    }
  }, [gameStatus, players, gameId, numPlayers, currentUserId]);

  if (_size(players) === numPlayers && !isUserInTheGame) {
    return <GameFullPage />;
  }

  if (gameStatus === GAME_STATUS.ENDED || gameStatus === GAME_STATUS.ABORTED) {
    return <Redirect href={`/game/${gameId}/result`} />;
  }

  if (gameStatus === GAME_STATUS.CREATED && _size(players) === 1) {
    if (isGameOwner) {
      return <WaitingForFriend game={game} />;
    }
    return <Loading label="Joining the Game..." />;
  }
  const Component = COMPONENT_FACTORY?.[gameStatus];

  if (gameStatus === GAME_STATUS.STARTED && gameMode !== GAME_MODES.PRACTICE) {
    return (
      <PlayGamePageWrapper>
        <Component game={game} />
      </PlayGamePageWrapper>
    );
  }

  if (!Component) {
    return null;
  }

  return <Component game={game} />;
};

const PlayGameMainLayout = ({
  COMPONENT_FACTORY,
}: {
  COMPONENT_FACTORY: any;
}) => <PlayGameMainLayoutWrapper COMPONENT_FACTORY={COMPONENT_FACTORY} />;

export default React.memo(PlayGameMainLayout);
