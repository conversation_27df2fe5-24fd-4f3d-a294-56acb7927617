import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: dark.colors.background,
  },
  headerExpanded: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 24,
    marginHorizontal: 16,
  },
  centeredMessageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 14,
    color: dark.colors.error,
    textAlign: 'center',
    fontFamily: 'Montserrat-400',
  },
  emptyText: {
    fontSize: 14,
    color: dark.colors.textDark,
    textAlign: 'center',
    fontFamily: 'Montserrat-400',
  },
  listHeaderContainer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: dark.colors.tertiary,
  },
  contactsFoundText: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
    textTransform: 'uppercase',
  },
  sendReqAllText: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    color: dark.colors.secondary,
    textTransform: 'uppercase',
  },
  popoverHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 18,
    gap: 8,
  },
  popoverTitle: {
    fontSize: 17,
    fontFamily: 'Montserrat-500',
    color: dark.colors.text,
  },
  closeButton: {},
  noOfCollegeFriends: {
    paddingHorizontal: 16,
  },
  noOfCollegeFriendsText: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textDark,
  },
});
