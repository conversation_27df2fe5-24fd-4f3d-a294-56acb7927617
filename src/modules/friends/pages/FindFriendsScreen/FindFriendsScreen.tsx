import React, { useCallback } from 'react';
import { ScrollView, View } from 'react-native';
import { useRouter } from 'expo-router';
import Header from '@/src/components/shared/Header';
import InteractivePrimaryButton from 'atoms/InteractivePrimaryButton';
import { ICON_TYPES } from 'atoms/Icon';
import dark from 'core/constants/themes/dark';
import useInviteFriendOnMatiks from 'core/hooks/useInviteFriendOnMatiks';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';

import WebBackButton from 'shared/WebBackButton';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import _isNil from 'lodash/isNil';
import useIsAddCollegeFeatureAvailable from '@/src/hooks/features/useIsAddCollegeFeatureAvailable';
import Analytics from 'core/analytics';
import styles from './FindFriendsScreen.style';
import FriendsRecommendationList from '../../components/FriendsRecommendationList';

const FindFriendsScreen = () => {
  const router = useRouter();
  const { user } = useSession();
  const isAddCollegeFeatureAvailable = useIsAddCollegeFeatureAvailable();

  const redirectToSearchMathlete = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.PROFILE.CLICKED_ON_SEARCH_BY_USERNAME);
    router.push('/search-mathletes');
  }, [router]);

  const hasInstitutionAdded = !_isNil(userReader.institutionId(user));

  const { isMobile: isCompactMode } = useMediaQuery();

  const { handleNormalShare } = useInviteFriendOnMatiks();

  const handleShare = useCallback(() => {
    handleNormalShare({
      eventToBeTracked:
        ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS
          .CLICKED_ON_FIND_FRIEND_SHARE_PROFILE,
    });
  }, [handleNormalShare]);

  const navigateToAddCollegeFriendsScreen = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.PROFILE.CLICKED_ON_FIND_FRIENDS_BUTTON);
    router.push(`/profile/${userReader.username(user)}/add-college`);
  }, [router, user]);

  return (
    <View
      style={[styles.container, !isCompactMode && { paddingHorizontal: 20 }]}
    >
      <Header title="Find your friends" />
      {!isCompactMode && (
        <View style={styles.headerExpanded}>
          <WebBackButton title="Find your friends" />
        </View>
      )}
      <ScrollView contentContainerStyle={styles.scrollContentContainer}>
        <View style={styles.actionButtonsContainer}>
          <InteractivePrimaryButton
            label="SEARCH BY USERNAME"
            onPress={redirectToSearchMathlete}
            iconConfig={{
              name: 'account-search-outline',
              type: ICON_TYPES.MATERIAL_COMMUNITY_ICONS,
              size: 20,
              color: dark.colors.textLight,
            }}
            buttonBorderBackgroundStyle={{
              backgroundColor: dark.colors.tertiary,
            }}
            buttonContentStyles={styles.buttonContentStyles}
            buttonContainerStyle={{ justifyContent: 'flex-start' }}
            labelStyle={styles.actionButtonLabel}
          />
          <InteractivePrimaryButton
            label="SHARE YOUR PROFILE"
            onPress={handleShare}
            iconConfig={{
              name: 'share',
              type: ICON_TYPES.FEATHER,
              size: 20,
              color: dark.colors.textLight,
            }}
            buttonContentStyles={styles.buttonContentStyles}
            buttonBorderBackgroundStyle={{
              backgroundColor: dark.colors.tertiary,
            }}
            labelStyle={styles.actionButtonLabel}
          />
          {isAddCollegeFeatureAvailable && !hasInstitutionAdded && (
            <InteractivePrimaryButton
              label="FIND COLLEGE FRIENDS"
              onPress={navigateToAddCollegeFriendsScreen}
              iconConfig={{
                name: 'school-outline',
                type: ICON_TYPES.IONICON,
                size: 20,
                color: dark.colors.textLight,
              }}
              buttonBorderBackgroundStyle={{
                backgroundColor: dark.colors.tertiary,
              }}
              buttonContentStyles={styles.buttonContentStyles}
              labelStyle={styles.actionButtonLabel}
            />
          )}
        </View>
        <FriendsRecommendationList />
      </ScrollView>
    </View>
  );
};

export default FindFriendsScreen;
