import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';

export default StyleSheet.create({
  cardContainer: {
    width: 150,
    borderRadius: 12,
    paddingVertical: 16,
    gap: 4,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
  },
  profileImage: {
    width: 52,
    height: 52,
    borderRadius: 26,
    marginBottom: 8,
  },
  profileImagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: dark.colors.placeholder,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  nameText: {
    fontSize: 14,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textLight,
    textAlign: 'center',
    marginBottom: 2,
  },
  usernameText: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
    textAlign: 'center',
    marginBottom: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    width: '100%',
  },
  actionButtonText: {
    color: dark.colors.secondary,
    fontSize: 11,
    fontFamily: 'Montserrat-500',
    textTransform: 'uppercase',
  },
});
