import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: dark.colors.background,
  },
  image: {
    width: 150,
    height: 150,
    resizeMode: 'contain',
  },
  title: {
    fontSize: 12,
    fontFamily: 'Montserrat-400',
    color: dark.colors.text,
    textAlign: 'center',
    marginBottom: 20,
    width: 250,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 22,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
  },
  buttonText: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: dark.colors.secondary,
    marginLeft: 8,
    lineHeight: 20,
  },
});

export default styles;
