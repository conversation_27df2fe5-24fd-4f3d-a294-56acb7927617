import React, { useCallback } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import dark from 'core/constants/themes/dark';
import styles from './NoCollegeFriendScreen.style';
import { useSession } from 'modules/auth/containers/AuthProvider';
import useInviteFriendOnMatiks from 'core/hooks/useInviteFriendOnMatiks';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import userReader from 'core/readers/userReader';

const schoolImage = require('assets/images/school.png');

const AddCollegeEmptyState = () => {
  const { user } = useSession();
  const userInstitution = userReader.institutionName(user);
  const { handleNormalShare } = useInviteFriendOnMatiks();

  const handleInviteFriend = useCallback(() => {
    handleNormalShare({
      eventToBeTracked:
        ANALYTICS_EVENTS.PROFILE
          .CLICKED_ON_INVITE_FRIEND_FROM_ADD_COLLEGE_FRIENDS_SCREEN,
    });
  }, [handleNormalShare]);

  return (
    <View style={styles.container}>
      <Image source={schoolImage} style={styles.image} />
      <Text style={styles.title}>
        {`Invite your friends to “${userInstitution}” college`}
      </Text>
      <TouchableOpacity style={styles.button} onPress={handleInviteFriend}>
        <MaterialIcons name="add" size={20} color={dark.colors.secondary} />
        <Text style={styles.buttonText}>Invite Friend</Text>
      </TouchableOpacity>
    </View>
  );
};

export default React.memo(AddCollegeEmptyState);
