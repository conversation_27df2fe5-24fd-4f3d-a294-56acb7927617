import React, { useCallback, useEffect, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import UserImage from '@/src/components/atoms/UserImage';
import dark from '@/src/core/constants/themes/dark';
import userReader from 'core/readers/userReader';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import { router } from 'expo-router';

import useSendFriendRequest from 'modules/friendsAndFollowers/hooks/mutations/useSendFriendRequest';
import { useAcceptFriendRequest } from 'modules/friendsAndFollowers/hooks/mutations/useAcceptFriendRequest';
import useWithdrawFriendRequest from 'modules/friendsAndFollowers/hooks/mutations/useWithdrawFriendRequest';
import { useRemoveFriend } from 'modules/friendsAndFollowers/hooks/mutations/useRemoveFriend';
import ConfirmationPopover from '@/src/components/shared/ConfirmationPopover';
import { closePopover } from 'molecules/Popover/Popover';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import Analytics from 'core/analytics';
import { FRIENDSHIP_STATUS } from '../../hooks/queries/useGetUsersOfMyInstitute';
import styles from './CollegeFriendListItem.style';
import { ACTION_STATES, BUTTON_CONFIG } from './friendshipStatusButtonConfig';

interface CollegeFriendListItemProps {
  userOutput: any;
  onRemove?: (userId: string) => void;
}

interface PopoverConfig {
  title: string;
  message: string;
  confirmText: string;
  confirmButtonType: 'primary' | 'danger';
}

const CollegeFriendListItem: React.FC<CollegeFriendListItemProps> = ({
  userOutput,
  onRemove,
}) => {
  const { userPublicDetails, friendshipStatus: initialFriendshipStatus } =
    userOutput ?? EMPTY_OBJECT;

  const [currentFriendshipStatus, setCurrentFriendshipStatus] = useState(
    initialFriendshipStatus,
  );
  const userId = userReader.id(userPublicDetails);

  const [isConfirmPopoverVisible, setIsConfirmPopoverVisible] = useState(false);
  const [popoverConfig, setPopoverConfig] = useState<PopoverConfig | null>(
    null,
  );
  const [popoverConfirmAction, setPopoverConfirmAction] = useState<
    (() => Promise<void>) | null
  >(null);

  const { sendFriendRequest, isSendingFriendRequest } = useSendFriendRequest();
  const { acceptFriendRequest, loading: isAcceptingFriendRequest } =
    useAcceptFriendRequest();
  const { withdrawFriendRequest, loading: isWithdrawingFriendRequest } =
    useWithdrawFriendRequest();
  const { removeFriend, loading: isRemovingFriend } = useRemoveFriend();

  useEffect(() => {
    setCurrentFriendshipStatus(initialFriendshipStatus);
  }, [initialFriendshipStatus]);

  const navigateToUserProfile = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.PROFILE.CLICKED_ON_USER_PROFILE_FROM_COLLEGE_FRIENDS_TAB,
    );
    closePopover?.();
    if (userReader.username(userPublicDetails)) {
      router.push(`/profile/${userReader.username(userPublicDetails)}`);
    }
  }, [userPublicDetails]);

  const currentActionConfig =
    BUTTON_CONFIG[currentFriendshipStatus as FRIENDSHIP_STATUS];

  const handleActionPress = useCallback(async () => {
    if (!userId || !currentActionConfig) return;
    const { action, confirmation } = currentActionConfig;

    const actions: { [key: string]: () => Promise<any> } = {
      [ACTION_STATES.SEND]: async () => {
        if (isSendingFriendRequest) return;
        await sendFriendRequest({ receiverId: userId });
        setCurrentFriendshipStatus(FRIENDSHIP_STATUS.PENDING_SENT);
      },
      [ACTION_STATES.WITHDRAW]: async () => {
        if (isWithdrawingFriendRequest) return;
        await withdrawFriendRequest({ receiverId: userId });
        setCurrentFriendshipStatus(FRIENDSHIP_STATUS.NOT_FRIENDS);
      },
      [ACTION_STATES.ACCEPT]: async () => {
        if (isAcceptingFriendRequest) return;
        await acceptFriendRequest({ senderId: userId });
        setCurrentFriendshipStatus(FRIENDSHIP_STATUS.FRIENDS);
      },
      [ACTION_STATES.UNFRIEND]: async () => {
        if (isRemovingFriend) return;
        await removeFriend({ receiverId: userId });
        setCurrentFriendshipStatus(FRIENDSHIP_STATUS.NOT_FRIENDS);
        onRemove?.(userId);
      },
    };

    if (confirmation) {
      setPopoverConfig(confirmation);
      setPopoverConfirmAction(() => actions[action]);
      setIsConfirmPopoverVisible(true);
    } else if (actions[action]) {
      try {
        await actions[action]();
      } catch (error) {
        // console.error(
        //   `Error performing action ${action} for user ${userId}:`,
        //   error,
        // );
      }
    }
  }, [
    userId,
    currentActionConfig,
    sendFriendRequest,
    isSendingFriendRequest,
    withdrawFriendRequest,
    isWithdrawingFriendRequest,
    acceptFriendRequest,
    isAcceptingFriendRequest,
    removeFriend,
    isRemovingFriend,
    onRemove,
  ]);

  const handlePopoverConfirm = async () => {
    if (popoverConfirmAction) {
      try {
        await popoverConfirmAction();
      } catch (error) {
        // console.error('Error executing popover confirm action:', error);
      }
    }
    setIsConfirmPopoverVisible(false);
    setPopoverConfirmAction(null);
    setPopoverConfig(null);
  };

  const handlePopoverCancel = () => {
    setIsConfirmPopoverVisible(false);
    setPopoverConfirmAction(null);
    setPopoverConfig(null);
  };

  const renderActionButton = () => {
    if (!currentActionConfig) {
      return null;
    }

    let isLoading = false;
    switch (currentActionConfig.action) {
      case ACTION_STATES.SEND:
        isLoading = isSendingFriendRequest;
        break;
      case ACTION_STATES.WITHDRAW:
        isLoading = isWithdrawingFriendRequest;
        break;
      case ACTION_STATES.ACCEPT:
        isLoading = isAcceptingFriendRequest;
        break;
      case ACTION_STATES.UNFRIEND:
        isLoading = isRemovingFriend;
        break;
      default:
        break;
    }

    const directActionLoading =
      (currentActionConfig.action === ACTION_STATES.SEND &&
        isSendingFriendRequest) ||
      (currentActionConfig.action === ACTION_STATES.ACCEPT &&
        isAcceptingFriendRequest);

    return (
      <InteractiveSecondaryButton
        onPress={handleActionPress}
        iconConfig={currentActionConfig.iconConfig}
        disabled={isLoading || directActionLoading}
        buttonBackgroundStyle={{
          backgroundColor: dark.colors.tertiary,
          width: 50,
        }}
        buttonContentStyle={{
          justifyContent: 'center',
          alignItems: 'center',
        }}
        buttonStyle={{ width: 50, height: 48 }}
      />
    );
  };

  if (!userPublicDetails) {
    return null;
  }

  return (
    <>
      <TouchableOpacity
        style={styles.itemContainer}
        onPress={navigateToUserProfile}
      >
        <UserImage user={userPublicDetails} style={styles.profileImage} />
        <View style={styles.userInfoContainer}>
          <Text style={styles.nameText} numberOfLines={1}>
            {userReader.displayName(userPublicDetails)}
          </Text>
          <Text style={styles.usernameText} numberOfLines={1}>
            @{userReader.username(userPublicDetails)}
          </Text>
        </View>
        {renderActionButton()}
      </TouchableOpacity>
      {popoverConfig && (
        <ConfirmationPopover
          isVisible={isConfirmPopoverVisible}
          title={popoverConfig.title}
          message={popoverConfig.message}
          confirmText={popoverConfig.confirmText}
          onConfirm={handlePopoverConfirm}
          onCancel={handlePopoverCancel}
          isConfirmDisabled={
            (currentActionConfig?.action === ACTION_STATES.WITHDRAW &&
              isWithdrawingFriendRequest) ||
            (currentActionConfig?.action === ACTION_STATES.UNFRIEND &&
              isRemovingFriend)
          }
          confirmButtonType={popoverConfig.confirmButtonType}
        />
      )}
    </>
  );
};

export default React.memo(CollegeFriendListItem);
