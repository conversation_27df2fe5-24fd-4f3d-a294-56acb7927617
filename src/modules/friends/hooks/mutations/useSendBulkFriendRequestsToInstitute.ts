import { gql, useMutation } from '@apollo/client';

const SEND_BULK_FRIEND_REQUESTS_TO_INSTITUTE = gql`
  mutation SendBulkFriendRequestsToInstitute {
    sendBulkFriendRequestsToInstitute
  }
`;

const useSendBulkFriendRequestsToInstitute = () => {
  const [sendRequests, { data, loading, error }] = useMutation(
    SEND_BULK_FRIEND_REQUESTS_TO_INSTITUTE,
  );

  return {
    sendBulkRequests: sendRequests,
    data,
    loading,
    error,
  };
};

export default useSendBulkFriendRequestsToInstitute;
