import { gql, useApolloClient, useQuery } from '@apollo/client';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import { useCallback, useEffect, useRef } from 'react';
import _isEqual from 'lodash/isEqual';
import _trim from 'lodash/trim';
import { USER_FRAGMENT } from '../graphql/fragments/user';
import { useSession } from '../../modules/auth/containers/AuthProvider';

export const GET_USER_BY_USERNAME = gql`
  query GetUserByUsername($username: String) {
    getUserByUsername(username: $username) {
      userPublicDetails {
        ...CoreUserFields
      }
      isFollowing
      friendshipStatus
    }
  }
  ${USER_FRAGMENT}
`;

const useGetUserByUserNameQuery = ({ userName }) => {
  const { user, refreshCurrentUser } = useSession();
  const { username: currentUsersUserName } = user ?? EMPTY_OBJECT;
  const apolloClient = useApolloClient();

  const isCurrentUser =
    !_isEmpty(userName) &&
    _isEqual(_trim(currentUsersUserName), _trim(userName));

  const isInvalidUsername = _isNil(userName) || _isEmpty(userName);

  const skipQuery = isInvalidUsername || isCurrentUser;

  const {
    data: searchedUserData,
    loading,
    error,
    refetch,
    client = apolloClient,
  } = useQuery(GET_USER_BY_USERNAME, {
    fetchPolicy: 'cache-first',
    variables: {
      username: userName ?? currentUsersUserName,
    },
    notifyOnNetworkStatusChange: true,
    skip: skipQuery,
  });

  const { getUserByUsername } = searchedUserData ?? EMPTY_OBJECT;

  const updateSearchedUserCache = useCallback(
    (updatedUserFields, additionalDetails) => {
      client.cache.updateQuery(
        {
          query: GET_USER_BY_USERNAME,
          variables: {
            username: userName,
          },
          broadcast: true,
          overwrite: true,
          optimistic: true,
        },
        (existingUserData) => {
          const { getUserByUsername: existingUser } =
            existingUserData ?? EMPTY_OBJECT;

          if (_isEmpty(existingUser) || _isNil(existingUser))
            return existingUserData;

          const { userPublicDetails, ...restUserData } =
            existingUser ?? EMPTY_OBJECT;

          return {
            getUserByUsername: {
              ...restUserData,
              userPublicDetails: {
                ...userPublicDetails,
                ...updatedUserFields,
              },
              ...additionalDetails,
            },
          };
        },
      );
    },
    [userName, client.cache],
  );

  const refreshCurrentUserRef = useRef(refreshCurrentUser);
  refreshCurrentUserRef.current = refreshCurrentUser;

  useEffect(() => {
    refreshCurrentUserRef.current();
  }, []);

  if (isCurrentUser || isInvalidUsername) {
    return {
      user,
      isCurrentUser: true,
      loading: false,
      error: null,
    };
  }

  return {
    user: getUserByUsername?.userPublicDetails,
    userAdditionalInfo: {
      isFollowing: getUserByUsername?.isFollowing,
      friendshipStatus: getUserByUsername?.friendshipStatus,
    },
    isCurrentUser,
    loading,
    error,
    updateSearchedUserCache,
    refetch,
  };
};

export default useGetUserByUserNameQuery;
