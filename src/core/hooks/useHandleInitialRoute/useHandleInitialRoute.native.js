import { useEffect, useState } from 'react';
import {
  getStorageState,
  setStorageItemAsync,
} from 'core/hooks/useStorageState';

import _isNil from 'lodash/isNil';
import { useRouter } from 'expo-router';
import * as Linking from 'expo-linking';
import { INITIAL_ROUTE_KEY } from '@/src/core/constants/appConstants';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { addEventListener, getInitialURL } from 'expo-linking/src/Linking';
import { isValidPathForDeeplink } from '../../utils/deepLinkUtils';

let appMounted = false;

const useHandleInitialRoute = () => {
  const router = useRouter();
  const [url, setUrl] = useState(null);
  const { isReady } = useSession();

  useEffect(() => {
    if (!isReady) {
      return;
    }

    if (!_isNil(url)) {
      const { path, queryParams } = Linking.parse(url);
      setUrl(null);
      if (!isValidPathForDeeplink(path)) {
        return;
      }
      router.push({
        pathname: path,
        params: queryParams,
      });
    } else {
      getStorageState(INITIAL_ROUTE_KEY).then((val) => {
        if (val) {
          setStorageItemAsync(INITIAL_ROUTE_KEY, null).then(() => {
            const { path, queryParams } = Linking.parse(val);
            setTimeout(() => {
              router.push(path);
              router.push({
                pathname: path,
                params: queryParams,
              });
            }, 50);
          });
        }
      });
    }
  }, [isReady, url]);

  useEffect(() => {
    if (appMounted) return;

    appMounted = true;
    getInitialURL().then((url) => setUrl(url));
    const subscription = addEventListener('url', (event) => setUrl(event?.url));
    return () => subscription.remove();
  }, []);
};

export default useHandleInitialRoute;
