/* eslint-disable import/prefer-default-export */
export const EVENTS = {
  CROSS_MATH_PUZZLE: {
    VIEWED_PUZZLE_HOME_PAGE: 'puzzle: view puzzle home page',
    VIEWED_CROSS_MATH_PUZZLE_PAGE: 'puzzle: view cross math puzzle page',
    CLICKED_ON_SOLVE_CROSS_MATH_PUZZLE:
      'puzzle: click on solve cross math puzzle',
    CLICKED_ON_SOLVE_CROSS_MATH_PUZZLE_CARD:
      'puzzle: click on solve daily cross math puzzle card',
    CLICKED_ON_SOLVE_CROSS_MATH_PUZZLE_CARD_HOME_PAGE:
      'puzzle: click on solve daily cross math puzzle card home page',
    ATTEMPTING_CROSS_MATH_PUZZLE: 'puzzle: attempting cross math puzzle',
    SOLVED_CROSS_MATH_PUZZLE: 'puzzle: solved cross math puzzle',
    ERROR_WHILE_SUBMITTING_CROSS_MATH_PUZZLE:
      'puzzle: error while submitting cross math puzzle',

    VIEWED_CROSS_MATH_RESULT_PAGE:
      'puzzle: viewed cross math puzzle result page',

    // calendar
    CLICKED_ON_DATE_PICKER: 'puzzle: click on date picker',
    CHANGED_PUZZLE_DATE: 'puzzle: changed puzzle date',

    // puzzle header
    CLICKED_ON_PUZZLE_INFO_ICON: 'puzzle: click on puzzle info icon',
    CLICKED_ON_CLEAR_PUZZLE: 'puzzle: click on clear puzzle',

    // puzzle result
    VIEWED_PUZZLE_RESULT: 'puzzle: view puzzle result',

    // whats new
    VIEW_PUZZLE_DUELS_IN_WHATS_NEW:
      'puzzle-duels: view puzzle duels in whats new section',
    CLICKED_ON_SEE_MORE: 'puzzle-duels: click on see more on whats new section',

    // puzzle-rush
    STARTED_PUZZLE_RUSH: 'puzzle-rush: start puzzle rush',
    ENDED_PUZZLE_RUSH: 'puzzle-rush: end puzzle rush',
    RESTARTED_PUZZLE_RUSH: 'puzzle-rush: restart puzzle rush',
    ERROR_ON_UPDATE_DAILY_PUZZLE_CACHE:
      'puzzle: error on update daily puzzle cache',
    ERROR_ON_UPDATE_PUZZLE_SUBMISSIONS_BY_MONTH_CACHE:
      'puzzle: error on update puzzle submissions by month cache',
  },

  MATH_MAZE_PUZZLE: {
    VIEWED_PUZZLE_HOME_PAGE: 'puzzle: view puzzle home page',
    VIEWED_MATH_MAZE_PUZZLE_PAGE: 'puzzle: view math maze puzzle page',
    CLICKED_ON_SOLVE_MATH_MAZE_PUZZLE:
      'puzzle: click on solve math maze puzzle',
    CLICKED_ON_SOLVE_KEN_KEN_PUZZLE_CARD:
      'puzzle: click on solve daily math maze puzzle card',
    ATTEMPTING_MATH_MAZE_PUZZLE: 'puzzle: attempting math maze puzzle',
    SOLVED_MATH_MAZE_PUZZLE: 'puzzle: solved math maze puzzle',
    SOLVING_MATH_MAZE_PUZZLE: 'puzzle: solving math maze puzzle',

    VIEWED_MATH_MAZE_RESULT_PAGE: 'puzzle: viewed math maze puzzle result page',
    ERROR_WHILE_SUBMITTING_MATH_MAZE_PUZZLE:
      'puzzle: error while submitting math maze puzzle',

    // calendar
    CLICKED_ON_DATE_PICKER: 'puzzle: click on date picker math maze puzzle',
    CHANGED_PUZZLE_DATE: 'puzzle: changed math maze puzzle date',

    // puzzle header
    CLICKED_ON_PUZZLE_INFO_ICON: 'puzzle: click on math maze puzzle info icon',
    CLICKED_ON_CLEAR_PUZZLE: 'puzzle: click on clear math maze puzzle',

    // puzzle result
    VIEWED_PUZZLE_RESULT: 'puzzle: view math maze puzzle result',

    // whats new
    VIEW_PUZZLE_DUELS_IN_WHATS_NEW:
      'puzzle-duels: view math maze puzzle duels in whats new section',
    CLICKED_ON_SEE_MORE:
      'puzzle-duels: click on see more on whats new section math maze puzzle',

    // puzzle-rush
    STARTED_PUZZLE_RUSH: 'puzzle-rush: start math maze puzzle rush',
    ENDED_PUZZLE_RUSH: 'puzzle-rush: end math maze puzzle rush',
    RESTARTED_PUZZLE_RUSH: 'puzzle-rush: restart math maze puzzle rush',
  },

  KEN_KEN_PUZZLE: {
    VIEWED_PUZZLE_HOME_PAGE: 'puzzle: view puzzle home page',
    VIEWED_KEN_KEN_PUZZLE_PAGE: 'puzzle: view ken ken puzzle page',
    CLICKED_ON_SOLVE_KEN_KEN_PUZZLE: 'puzzle: click on solve ken ken puzzle',
    CLICKED_ON_SOLVE_KEN_KEN_PUZZLE_CARD:
      'puzzle: click on solve daily ken ken puzzle card',
    ATTEMPTING_KEN_KEN_PUZZLE: 'puzzle: attempting ken ken puzzle',
    SOLVED_KEN_KEN_PUZZLE: 'puzzle: solved ken ken puzzle',
    SOLVING_KEN_KEN_PUZZLE: 'puzzle: solving ken ken puzzle',

    VIEWED_KEN_KEN_RESULT_PAGE: 'puzzle: viewed ken ken puzzle result page',
    ERROR_WHILE_SUBMITTING_KEN_KEN_PUZZLE:
      'puzzle: error while submitting ken ken puzzle',

    // calendar
    CLICKED_ON_DATE_PICKER: 'puzzle: click on date picker ken ken puzzle',
    CHANGED_PUZZLE_DATE: 'puzzle: changed ken ken puzzle date',

    // puzzle header
    CLICKED_ON_PUZZLE_INFO_ICON: 'puzzle: click on ken ken puzzle info icon',
    CLICKED_ON_CLEAR_PUZZLE: 'puzzle: click on clear ken ken puzzle',

    // puzzle result
    VIEWED_PUZZLE_RESULT: 'puzzle: view ken ken puzzle result',

    // whats new
    VIEW_PUZZLE_DUELS_IN_WHATS_NEW:
      'puzzle-duels: view ken ken puzzle duels in whats new section',
    CLICKED_ON_SEE_MORE:
      'puzzle-duels: click on see more on whats new section ken ken puzzle',

    // puzzle-rush
    STARTED_PUZZLE_RUSH: 'puzzle-rush: start ken ken puzzle rush',
    ENDED_PUZZLE_RUSH: 'puzzle-rush: end ken ken puzzle rush',
    RESTARTED_PUZZLE_RUSH: 'puzzle-rush: restart ken ken puzzle rush',
  },
};
