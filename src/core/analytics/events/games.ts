import { GAME_TYPES } from 'core/constants/gameTypes';

export const EVENTS = {
  [GAME_TYPES.PLAY_ONLINE]: {
    VIEWED: 'view play online duels config page',
    CLICK_ON_CREATE_GAME: 'click on create online duels game',
    CLICKED_ON_BACK: 'click on back',
  },
  [GAME_TYPES.DMAS]: {
    VIEWED: 'view play online duels config page',
    CLICK_ON_CREATE_GAME: 'click on create online duels game',
    CLICKED_ON_BACK: 'click on back',
  },

  // PLAY-WITH-FRIEND
  [GAME_TYPES.PLAY_WITH_FRIEND]: {
    VIEWED: 'view play friend config page',
    CLICK_ON_CREATE_GAME: 'click on create play with friend game',
    CLICKED_ON_BACK: 'click on back',

    VIEW_PWF_WAITING_PAGE: 'view play with friend waiting page',
    CLICKED_ON_COPY_LINK: 'click on copy link',
    CLICKED_ON_CANCEL_GAME: 'click on cancel game',
  },

  [GAME_TYPES.FASTEST_FINGER]: {
    VIEWED: 'view play fastest finger config page',
    CLICK_ON_CREATE_GAME: 'click on create play fastest finger game',
    CLICKED_ON_BACK: 'click on back',

    VIEW_PWF_WAITING_PAGE: 'view play fastest finger waiting page',
    CLICKED_ON_COPY_LINK: 'click on copy link',
    CLICKED_ON_CANCEL_GAME: 'click on cancel game',
  },

  // GROUP PLAY
  [GAME_TYPES.GROUP_PLAY]: {
    VIEWED: 'view group play config page',
    CLICK_ON_CREATE_GAME: 'click on create group play game',
    CLICKED_ON_BACK: 'click on back',

    VIEW_PWF_WAITING_PAGE: 'view play group play waiting page',
    CLICKED_ON_COPY_LINK: 'click on copy link',
    CLICKED_ON_CANCEL_GAME: 'click on cancel game',
  },

  // PRACTICE
  [GAME_TYPES.PRACTICE]: {
    VIEWED: 'view practice based on rating config page',
    CLICK_ON_CREATE_GAME: 'click on create practice based on rating game',
    CLICKED_ON_BACK: 'click on back',
  },

  // Ability Duels
  [GAME_TYPES.ABILITY_DUELS]: {
    // whats new
    CLICKED_ON_SEE_MORE: 'whats-new: click on see more on whats new section',
    VIEWED: 'view ability duels based on rating config page',
    CLICK_ON_CREATE_GAME: 'click on create ability duels based on rating game',
    CLICKED_ON_BACK: 'click on back',
  },

  [GAME_TYPES.DMAS_ABILITY]: {
    // whats new
    CLICKED_ON_SEE_MORE: 'whats-new: click on see more on whats new section',
    VIEWED: 'view ability duels based on rating config page',
    CLICK_ON_CREATE_GAME: 'click on create ability duels based on rating game',
    CLICKED_ON_BACK: 'click on back',
  },

  // rematch
  REMATCH_REQUEST_OVERLAY: {
    CLICK_ON_ACCEPT_REMATCH_REQUEST: 'click on accept rematch request',
    CLICK_ON_REJECT_REMATCH_REQUEST: 'click on reject rematch request',
    OPPONENT_ACCEPTED_REMATCH_REQUEST: 'opponent accepted rematch request',
    OPPONENT_REJECTED_REMATCH_REQUEST: 'opponent rejected rematch request',
    RECEIVED_REMATCH_REQUEST: 'received rematch request',
  },

  // game result page
  RESULT_MODAL: {
    CLICKED_ON_GO_HOME_ON_RESULT_MODAL: 'click on result modal go home',
    CLICKED_ON_NEW_GAME_ON_RESULT_MODAL: 'click on result modal new game',
    CLICKED_ON_REMATCH_REQUEST: 'click on rematch request',
  },

  VIEW_SEARCHING_FOR_OPPONENT: 'view searching for mathlete',
  OPPONENT_FOUND: 'Opponent Found',
  PUZZLE_GAME_OPPONENT_FOUND: 'Puzzle Game Opponent Found',
  GAME_STARTED: 'Game Started',
  GAME_ENDED: 'Game Ended',
  GAME_ABORTED: 'Game Aborted',
  ASKED_USER_STILL_IN_GAME: 'Asked User Still In Game',
  ENDING_GAME_FROM_CLIENT: 'game: Ending Game From Client',
  ABORTED_OPPONENT_SEARCH: 'click on abort searching mathletes',
};
