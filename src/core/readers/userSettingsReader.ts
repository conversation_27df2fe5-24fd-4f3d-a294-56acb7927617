import _get from 'lodash/get';
import { userSettingsTypes } from 'core/types/userSettings';
import { UserSettingsKeyboardType } from '@/src/components/shared/CustomKeyboard/types';

const userSettingsReader = {
  playSound: (userSetting: userSettingsTypes) =>
    _get(userSetting, ['playSound'], true),
  hapticFeedback: (userSetting: userSettingsTypes) =>
    _get(userSetting, ['hapticFeedback'], true),
  keyboardType: (userSetting: userSettingsTypes) =>
    _get(userSetting, ['keyboardType'], UserSettingsKeyboardType.TELEPHONE),
};

export default userSettingsReader;
