import { Platform } from 'react-native';
import _property from 'lodash/property';
import _get from 'lodash/get';
import _find from 'lodash/find';

const userReader = {
  id: _property('_id'),
  email: _property('email'),
  displayName: _property('name'),
  isSignUp: _property('isSignup'),
  isGuest: _property('isGuest'),
  rating: (user) =>
    _get(user, ['ratingV2', 'globalRating']) ?? _get(user, 'rating', 1000),
  ratingV2: (user) => _get(user, 'ratingV2', EMPTY_OBJECT),
  staticPoints: _property('statikCoins'),
  username: (user) => _get(user, 'username') ?? _get(user, 'name', 'Anonymous'),
  profileImageUrl: _property('profileImageUrl'),
  hasFixedRating: _property('hasFixedRating'),
  statikCoins: _property('statikCoins'),
  globalRank: _property('globalRank'),
  gamesPlayed: _property('stats.ngp'),
  userStreaks: _property('userStreaks'),
  highestRating: _property('stats.hr'),
  currentStreak: _property('userStreaks.currentStreak'),
  longestStreak: _property('userStreaks.longestStreak'),
  streakFreezers: _property('userStreaks.streakFreezers'),
  pushNotificationToken: (user) => {
    const allTokens = _get(user, 'pushNotificationTokens');
    const platform = Platform.OS;
    const platformTokenObj = _find(
      allTokens,
      (tokenObj) => _get(tokenObj, 'platformId') === platform,
    );

    return _get(platformTokenObj, 'pNT', '');
  },
  abilityDuelsRating: (user) =>
    _get(user, ['ratingV2', 'abilityDuelsRating'], 1000),
  flashAnzanRating: (user) =>
    _get(user, ['ratingV2', 'flashAnzanRating'], 1000),
  puzzleRating: (user) => _get(user, ['ratingV2', 'puzzleRating'], 1000),
  userCurrentLeague: (user) => _get(user, ['league', 'league'], 'BRONZE'),
  userCurrLeagueInfo: (user) => _get(user, ['league'], EMPTY_OBJECT),
  userCoinsTillLastWeek: (user) =>
    _get(user, ['league', 'coinsTillLastWeek'], 0),
  userHasParticipatedInLeague: (user) =>
    _get(user, ['league', 'hasParticipated'], false),
  hasUnlockedAllGames: (user) =>
    _get(user, ['additional', 'hasUnlockedAllGames'], false) ||
    _get(user, ['stats', 'ngp'], 0) >= 3,
  needToShowQuickLinks: (user) => _get(user, ['stats', 'ngp'], 0) >= 10,
  needToShowOnlineUsers: (user) => _get(user, ['stats', 'ngp'], 0) >= 3,
  canParticipateInTournaments: (user) =>
    _get(user, ['additional', 'hasUnlockedAllGames'], false) &&
    !_get(user, 'isGuest', false),
  bio: _property('bio'),
  links: _property('links'),
  league: _property('league.league'),
  referralCode: _property('referralCode'),
  badge: _property('badge'),
  friendsCount: (user) => _get(user, ['stats', 'friendsCount'], 0),
  followingCount: (user) => _get(user, ['stats', 'followingCount'], 0),
  followersCount: (user) => _get(user, ['stats', 'followersCount'], 0),
  isDeletedAccount: (user) => _get(user, 'isDeleted', false),
  institutionId: _property('institutionId'),
  institutionName: _property('institutionName'),
};

export default userReader;
