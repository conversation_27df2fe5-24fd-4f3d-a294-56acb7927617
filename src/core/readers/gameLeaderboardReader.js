import _get from 'lodash/get';

const gameLeaderboardReader = {
  userId: (leaderboardEntry) => _get(leaderboardEntry, 'userId', ''),
  rank: (leaderboardEntry) => _get(leaderboardEntry, 'rank', 0),
  correct: (leaderboardEntry) => _get(leaderboardEntry, 'correct', 0),
  incorrect: (leaderboardEntry) => _get(leaderboardEntry, 'incorrect', 0),
  incorrectAttempts: (leaderboardEntry) =>
    _get(leaderboardEntry, 'incorrectAttempts', 0),
  totalPoints: (leaderboardEntry) => _get(leaderboardEntry, 'totalPoints', 0),
  ratingChange: (leaderboardEntry) => _get(leaderboardEntry, 'ratingChange', 0),
  statikCoinsEarned: (leaderboardEntry) =>
    _get(leaderboardEntry, 'statikCoinsEarned', 0),

  isWinner: (leaderboardEntry) => _get(leaderboardEntry, 'rank') === 1,

  getScore: (leaderboardEntry, isFlashAnzan = false) =>
    isFlashAnzan
      ? _get(leaderboardEntry, 'totalPoints', 0)
      : _get(leaderboardEntry, 'correct', 0),

  findEntryForPlayer: (leaderboard, playerId) => {
    if (!leaderboard || !playerId) return EMPTY_OBJECT;
    return (
      leaderboard.find((entry) => entry?.userId === playerId) || EMPTY_OBJECT
    );
  },
};

export default gameLeaderboardReader;
