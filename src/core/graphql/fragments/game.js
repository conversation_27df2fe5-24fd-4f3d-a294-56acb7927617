/* eslint-disable import/prefer-default-export */
import { gql } from '@apollo/client';

export const GAME_FRAGMENT = gql`
  fragment CoreGameFields on Game {
    _id
    players {
      userId
      rating
      statikCoins
      status
      timeLeft
    }
    gameStatus
    gameMode
    gameCategory
    rematchRequestedBy
    gameType
    createdBy
    config {
      timeLimit
      numPlayers
      maxTimePerQuestion
      categorySpecificConfig {
        category
        blitz {
          timeLimit
        }
        classical {
          timeLimit
        }
        memory {
          timeLimit
        }
        puzzle {
          timeLimit
        }
      }
      gameTypeSpecificConfig {
        type
      }
      modeSpecificConfig {
        mode
        onlineChallenge {
          numPlayers
        }
        onlineSearch {
          numPlayers
        }
        groupPlay {
          difficultyLevel
          maxGapBwGame
          maxPlayers
          numPlayers
          maxTimePerQuestion
          maxGapBwGame
          questionTags
          minPlayers
        }
        practice {
          numPlayers
        }
        playViaLink {
          numPlayers
        }
      }
    }
    minifiedQuestions
    encryptedQuestions
    leaderBoard {
      userId
      correct
      incorrect
      incorrectAttempts
      totalPoints
      ratingChange
      statikCoinsEarned
      rank
    }
    startTime
    endTime
    seriesId
    showdownId
    showdownGameConfig {
      isRoundEnded
      hasOpponentNotShown
      nextGameId
      round
      nextGameStartsAt
      totalGamesPlayed
      showdownGamePlayer {
        isTie
        isWinner
        userId
        wins
        score
      }
      numOfGames
    }
  }
`;
