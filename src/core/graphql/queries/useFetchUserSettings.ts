import { gql, useQuery } from '@apollo/client'

export const GET_USER_SETTINGS = gql`
  query GetUserSettings {
    getUserSettings {
      playSound
      hapticFeedback
      keyboardType
    }
  }
`

const useFetchUserSettings = () => {
  const { data, loading, error } = useQuery(GET_USER_SETTINGS, {
    fetchPolicy: 'cache-first',
    notifyOnNetworkStatusChange: true,
  })

  const userSettings = data?.getUserSettings

  return {
    userSettings,
    loading,
    error,
  }
}

export default useFetchUserSettings
