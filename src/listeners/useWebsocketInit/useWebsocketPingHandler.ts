import { WEBSOCKET_CHANNELS } from '@/src/core/constants/websocket';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { usePathname } from 'expo-router';
import { useRef, useEffect, useCallback } from 'react';
import _toNumber from 'lodash/toNumber';
import _isEmpty from 'lodash/isEmpty';
import _toString from 'lodash/toString';
import { getUserCurrentActivity } from '@/src/core/utils/getUserCurrentActivity';
import { WebsocketMessageType } from '@/src/store/useWebSocketStore/handlers';
import useWebsocketStore from '@/src/store/useWebSocketStore';

let serverOffset = 0;
global.getCurrentTime = () => Date.now() + _toNumber(serverOffset);

const useWebsocketPingHandler = () => {
  const { userId }: any = useSession();

  const { sendMessage } = useWebsocketStore((state) => ({
    sendMessage: state.sendMessage,
  }));

  const sendMessageRef = useRef(sendMessage);
  sendMessageRef.current = sendMessage;

  const pingPongChannel = WEBSOCKET_CHANNELS.PingPong(userId);

  const currentUrl = usePathname();
  const pingPongTimeoutRef = useRef<any>(null);

  const handleServerTimeOffset = useCallback(({ data }: { data: any }) => {
    const clientTime = Date.now();
    if (data && !serverOffset) {
      const { clientRequestTime, serverResponseTime } = data ?? EMPTY_OBJECT;
      const RoundTripTime = clientTime - clientRequestTime;
      serverOffset = serverResponseTime + RoundTripTime / 2 - clientTime;
    }
  }, []);

  useEffect(() => {
    if (pingPongTimeoutRef.current) {
      clearInterval(pingPongTimeoutRef.current);
    }
    if (_isEmpty(userId)) {
      return;
    }
    pingPongTimeoutRef.current = setInterval(() => {
      sendMessageRef.current({
        type: WebsocketMessageType.PING_PONG,
        channel: pingPongChannel,
        data: {
          clientTime: Date.now(),
          currentActivity: getUserCurrentActivity({ currentUrl }),
          userId: _toString(userId),
        },
      });
    }, 5000);
    return () => {
      clearInterval(pingPongTimeoutRef.current);
    };
  }, [userId, currentUrl, pingPongChannel]);

  return {
    handleServerTimeOffset,
  };
};

export default useWebsocketPingHandler;
