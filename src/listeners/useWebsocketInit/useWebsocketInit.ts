import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import useWebsocketStore from 'store/useWebSocketStore';
import EventManager from '@/src/core/event';
import { WEBSOCKET_CHANNELS } from '@/src/core/constants/websocket';
import { getChannelType } from './utils';
import handleEventEmitter from './eventEmmiters';
import useWebsocketMonitoring from './useWebsocketMonitoring';
import useWebsocketPingHandler from './useWebsocketPingHandler';

const useWebsocketInit = () => {
  const { session, userId }: any = useSession();

  const pingPongChannel = WEBSOCKET_CHANNELS.PingPong(userId);
  const { connect, cleanUp, updateLastMessage } = useWebsocketStore(
    (state) => ({
      connect: state.connect,
      cleanUp: state.cleanUp,
      updateLastMessage: state.updateLastMessage,
    }),
  );

  const cleanUpRef = useRef(cleanUp);
  cleanUpRef.current = cleanUp;

  const connectRef = useRef(connect);
  connectRef.current = connect;

  useWebsocketMonitoring();
  const { handleServerTimeOffset } = useWebsocketPingHandler();
  const handleServerTimeOffsetRef = useRef(handleServerTimeOffset);
  handleServerTimeOffsetRef.current = handleServerTimeOffset;
  const emitter = useMemo(() => new EventManager(), []);

  const onMessage = useCallback(
    (event: any) => {
      try {
        const parsed = JSON.parse(event?.data ?? '');
        const data = JSON.parse(parsed?.data);
        const channel = parsed?.channel;
        const channelType = getChannelType(channel);
        if (channel === pingPongChannel) {
          handleServerTimeOffsetRef.current({ data });
        }
        handleEventEmitter(channelType, emitter, data);
        updateLastMessage(channel, data);
      } catch (error) {
        console.error('Error parsing message:', error);
        Analytics.track(ANALYTICS_EVENTS.WEB_SOCKET.ON_ERROR_IN_JSON_PARSE);
      }
    },
    [emitter, updateLastMessage, pingPongChannel],
  );
  const onMessageRef = useRef(onMessage);
  onMessageRef.current = onMessage;

  useEffect(() => {
    connectRef.current?.(session, onMessageRef.current);
    return () => {
      cleanUpRef.current?.();
    };
  }, [session]);

  return null;
};

export default useWebsocketInit;
